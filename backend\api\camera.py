from fastapi import APIRouter, HTTPException, WebSocket, Query, Depends, UploadFile, File
from pydantic import BaseModel
from typing import List, Dict, Optional
import asyncio
import base64
import os
from datetime import datetime

# 创建路由
router = APIRouter()

# 摄像头配置模型
class CameraSettings(BaseModel):
    resolution: str = "640x480"
    fps: int = 30
    brightness: int = 50
    contrast: int = 50

# 摄像头信息模型
class CameraInfo(BaseModel):
    id: str
    name: str
    is_active: bool = False

# 模拟摄像头列表
cameras = [
    CameraInfo(id="cam1", name="摄像头 1"),
    CameraInfo(id="cam2", name="摄像头 2")
]

# 活跃摄像头ID
active_camera_id = "cam1"

# 摄像头设置
camera_settings = CameraSettings()

# 截图保存目录
SNAPSHOT_DIR = os.environ.get("SNAPSHOT_DIR", "./snapshots")
os.makedirs(SNAPSHOT_DIR, exist_ok=True)

# 获取摄像头列表
@router.get("/list")
async def get_cameras():
    """
    获取可用摄像头列表
    """
    global cameras, active_camera_id
    
    # 更新活跃状态
    for camera in cameras:
        camera.is_active = (camera.id == active_camera_id)
    
    return cameras

# 选择摄像头
@router.post("/select/{camera_id}")
async def select_camera(camera_id: str):
    """
    选择要使用的摄像头
    """
    global active_camera_id, cameras
    
    # 检查摄像头是否存在
    camera_exists = any(camera.id == camera_id for camera in cameras)
    if not camera_exists:
        raise HTTPException(status_code=404, detail=f"摄像头不存在: {camera_id}")
    
    # 设置活跃摄像头
    active_camera_id = camera_id
    
    return {
        "status": "success",
        "message": f"已切换到摄像头: {camera_id}"
    }

# 获取摄像头设置
@router.get("/settings")
async def get_camera_settings():
    """
    获取摄像头设置
    """
    global camera_settings
    return camera_settings

# 更新摄像头设置
@router.put("/settings")
async def update_camera_settings(settings: CameraSettings):
    """
    更新摄像头设置
    """
    global camera_settings
    
    camera_settings = settings
    
    return {
        "status": "success",
        "message": "摄像头设置已更新",
        "settings": camera_settings
    }

# 截图
@router.post("/snapshot")
async def take_snapshot():
    """
    从当前活跃摄像头拍摄截图
    """
    global active_camera_id
    
    try:
        # 在实际应用中，这里应该调用摄像头API获取图像
        # 这里我们只是模拟这个过程
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{active_camera_id}_{timestamp}.jpg"
        filepath = os.path.join(SNAPSHOT_DIR, filename)
        
        # 模拟截图保存
        # 在实际应用中，你需要将实际图像数据写入文件
        with open(filepath, "w") as f:
            f.write("Mock image data")
        
        return {
            "status": "success",
            "message": "截图成功",
            "filename": filename,
            "filepath": filepath
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"截图失败: {str(e)}")

# 获取截图列表
@router.get("/snapshots")
async def list_snapshots():
    """
    获取所有保存的截图列表
    """
    try:
        snapshots = []
        for filename in os.listdir(SNAPSHOT_DIR):
            if filename.endswith(('.jpg', '.jpeg', '.png')):
                filepath = os.path.join(SNAPSHOT_DIR, filename)
                snapshots.append({
                    "filename": filename,
                    "timestamp": os.path.getmtime(filepath),
                    "size": os.path.getsize(filepath)
                })
        
        # 按时间戳排序（最新的在前）
        snapshots.sort(key=lambda x: x["timestamp"], reverse=True)
        
        return snapshots
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取截图列表失败: {str(e)}")

# 删除截图
@router.delete("/snapshot/{filename}")
async def delete_snapshot(filename: str):
    """
    删除指定的截图
    """
    try:
        filepath = os.path.join(SNAPSHOT_DIR, filename)
        
        # 检查文件是否存在
        if not os.path.exists(filepath):
            raise HTTPException(status_code=404, detail=f"截图不存在: {filename}")
        
        # 删除文件
        os.remove(filepath)
        
        return {
            "status": "success",
            "message": f"截图已删除: {filename}"
        }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"删除截图失败: {str(e)}") 