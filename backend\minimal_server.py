#!/usr/bin/env python
# 最小化服务器

from fastapi import FastAPI
from fastapi.responses import HTMLResponse
import uvicorn

app = FastAPI()

@app.get("/")
async def root():
    return {"message": "AI机械臂控制系统已启动", "status": "running"}

@app.get("/test")
async def test():
    return HTMLResponse("""
    <html>
        <head><title>测试页面</title></head>
        <body>
            <h1>🤖 AI机械臂控制系统测试</h1>
            <p>服务器运行正常！</p>
            <p>时间: <span id="time"></span></p>
            <script>
                document.getElementById('time').textContent = new Date().toLocaleString();
            </script>
        </body>
    </html>
    """)

if __name__ == "__main__":
    print("启动最小化服务器...")
    print("访问: http://localhost:8088")
    print("测试页面: http://localhost:8088/test")
    uvicorn.run(app, host="0.0.0.0", port=8088)
