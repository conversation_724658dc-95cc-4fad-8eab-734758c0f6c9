#!/usr/bin/env python
# 豆包API测试脚本
# 专门测试豆包API连接和功能

import os
import sys
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

def load_api_key():
    """加载API密钥"""
    try:
        api_key_path = Path(__file__).parent / "api接入" / "apikey.md"
        with open(api_key_path, 'r', encoding='utf-8') as f:
            return f.read().strip()
    except Exception as e:
        print(f"❌ 无法加载API密钥: {e}")
        return None

def test_doubao_api_direct():
    """直接测试豆包API"""
    print("=" * 60)
    print("直接测试豆包API")
    print("=" * 60)
    
    api_key = load_api_key()
    if not api_key:
        return False
    
    print(f"API密钥: {api_key[:10]}...{api_key[-10:]}")
    
    # API配置
    base_url = "https://ark.cn-beijing.volces.com/api/v3"
    model = "doubao-seed-1-6-250615"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 测试消息
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": "你好，请简单回复'测试成功'"
            }
        ],
        "temperature": 0.1,
        "max_tokens": 100,
        "stream": False
    }
    
    try:
        print("发送API请求...")
        response = requests.post(
            f"{base_url}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API请求成功")
            
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"AI响应: {content}")
                return True
            else:
                print(f"❌ 响应格式异常: {result}")
                return False
        else:
            print(f"❌ API请求失败")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求异常: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def test_doubao_with_image():
    """测试豆包API图像理解功能"""
    print("\n" + "=" * 60)
    print("测试豆包API图像理解功能")
    print("=" * 60)
    
    api_key = load_api_key()
    if not api_key:
        return False
    
    # 创建测试图像
    try:
        from PIL import Image
        import numpy as np
        import base64
        import io
        
        # 创建一个简单的测试图像
        test_image = Image.fromarray(
            np.random.randint(0, 255, (200, 200, 3), dtype=np.uint8)
        )
        
        # 转换为base64
        buffer = io.BytesIO()
        test_image.save(buffer, format='JPEG', quality=85)
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        image_data_url = f"data:image/jpeg;base64,{image_base64}"
        
        print("✅ 测试图像创建成功")
        
    except Exception as e:
        print(f"❌ 测试图像创建失败: {e}")
        return False
    
    # API配置
    base_url = "https://ark.cn-beijing.volces.com/api/v3"
    model = "doubao-seed-1-6-250615"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 测试消息（包含图像）
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "请描述这张图像，并回复'图像理解测试成功'"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_data_url
                        }
                    }
                ]
            }
        ],
        "temperature": 0.1,
        "max_tokens": 200,
        "stream": False
    }
    
    try:
        print("发送图像理解API请求...")
        response = requests.post(
            f"{base_url}/chat/completions",
            headers=headers,
            json=payload,
            timeout=60  # 图像处理可能需要更长时间
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 图像理解API请求成功")
            
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"AI响应: {content}")
                return True
            else:
                print(f"❌ 响应格式异常: {result}")
                return False
        else:
            print(f"❌ 图像理解API请求失败")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 图像理解测试异常: {e}")
        return False

def test_doubao_client_class():
    """测试豆包客户端类"""
    print("\n" + "=" * 60)
    print("测试豆包客户端类")
    print("=" * 60)
    
    try:
        from api.doubao_client import DoubaoClient
        
        # 创建客户端
        client = DoubaoClient()
        print("✅ 豆包客户端创建成功")
        
        # 测试连接
        success, message = client.test_connection()
        if success:
            print("✅ 豆包客户端连接测试成功")
            print(f"   消息: {message}")
            
            # 测试简单对话
            try:
                response = client.send_message("请回复'客户端测试成功'")
                print("✅ 豆包客户端对话测试成功")
                print(f"   AI响应: {response[:100]}...")
                return True
            except Exception as e:
                print(f"❌ 豆包客户端对话测试失败: {e}")
                return False
        else:
            print(f"❌ 豆包客户端连接测试失败: {message}")
            return False
            
    except Exception as e:
        print(f"❌ 豆包客户端类测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🤖 豆包API专项测试")
    print("=" * 60)
    
    test_results = []
    
    # 1. 直接API测试
    result = test_doubao_api_direct()
    test_results.append(("直接API测试", result))
    
    # 2. 图像理解测试
    result = test_doubao_with_image()
    test_results.append(("图像理解测试", result))
    
    # 3. 客户端类测试
    result = test_doubao_client_class()
    test_results.append(("客户端类测试", result))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！豆包API就绪。")
    elif passed >= 1:
        print("⚠️  部分测试通过，豆包API基本可用。")
    else:
        print("❌ 所有测试失败，请检查API配置。")
    
    print("\n📋 如果测试失败，请检查:")
    print("1. API密钥是否正确 (backend/api接入/apikey.md)")
    print("2. 网络连接是否正常")
    print("3. 模型ID是否正确")

if __name__ == "__main__":
    main()
