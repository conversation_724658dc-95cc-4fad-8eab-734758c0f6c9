#!/usr/bin/env python
# 机械臂遥控API - 平台后端与香橙派服务集成

import logging
import httpx
from fastapi import APIRouter, HTTPException, Body
from pydantic import BaseModel
from typing import Dict, Any, Optional

# 创建路由
router = APIRouter()

# 请求模型定义
class TeleoperationRequest(BaseModel):
    orange_pi_ip: str
    orange_pi_port: str = "8001"
    robot_type: str = "so100"
    robot_port: str = "/dev/ttyACM0"
    teleop_time_s: Optional[float] = None
    fps: Optional[int] = 200
    display_data: bool = False

class TeleoperationStopRequest(BaseModel):
    orange_pi_ip: str
    orange_pi_port: str = "8001"
    session_id: str

class TeleoperationStatusRequest(BaseModel):
    orange_pi_ip: str
    orange_pi_port: str = "8001"

# API端点
@router.post("/start")
async def start_teleoperation(request: TeleoperationRequest):
    """启动机械臂遥控"""
    if not request.orange_pi_ip:
        raise HTTPException(status_code=400, detail="缺少必要参数: orange_pi_ip")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            # 构建请求URL和数据
            url = f"http://{request.orange_pi_ip}:{request.orange_pi_port}/api/teleoperate/start"
            
            # 准备请求数据，移除orange_pi相关字段
            data = request.dict()
            data.pop("orange_pi_ip")
            data.pop("orange_pi_port")
            
            # 发送请求到香橙派服务
            response = await client.post(url, json=data)
            response.raise_for_status()
            
            # 返回结果
            return response.json()
    except httpx.HTTPStatusError as e:
        logging.error(f"HTTP错误 [{e.response.status_code}]: {e.response.text}")
        try:
            detail = e.response.json()
        except:
            detail = {"detail": e.response.text or "未知错误"}
        raise HTTPException(status_code=e.response.status_code, detail=detail)
    except httpx.RequestError as e:
        logging.error(f"请求错误: {str(e)}")
        raise HTTPException(status_code=503, detail=f"无法连接到遥控服务: {str(e)}")
    except Exception as e:
        logging.error(f"启动遥控时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动遥控失败: {str(e)}")

@router.post("/stop")
async def stop_teleoperation(request: TeleoperationStopRequest):
    """停止机械臂遥控"""
    if not request.orange_pi_ip or not request.session_id:
        raise HTTPException(status_code=400, detail="缺少必要参数: orange_pi_ip 或 session_id")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            # 构建请求URL和数据
            url = f"http://{request.orange_pi_ip}:{request.orange_pi_port}/api/teleoperate/stop"
            
            # 只发送session_id到香橙派服务
            data = {"session_id": request.session_id}
            
            # 发送请求到香橙派服务
            response = await client.post(url, json=data)
            response.raise_for_status()
            
            # 返回结果
            return response.json()
    except httpx.HTTPStatusError as e:
        logging.error(f"HTTP错误 [{e.response.status_code}]: {e.response.text}")
        try:
            detail = e.response.json()
        except:
            detail = {"detail": e.response.text or "未知错误"}
        raise HTTPException(status_code=e.response.status_code, detail=detail)
    except httpx.RequestError as e:
        logging.error(f"请求错误: {str(e)}")
        raise HTTPException(status_code=503, detail=f"无法连接到遥控服务: {str(e)}")
    except Exception as e:
        logging.error(f"停止遥控时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"停止遥控失败: {str(e)}")

@router.post("/status")
async def get_teleoperation_status(request: TeleoperationStatusRequest):
    """获取遥控状态"""
    if not request.orange_pi_ip:
        raise HTTPException(status_code=400, detail="缺少必要参数: orange_pi_ip")
    
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            # 构建请求URL
            url = f"http://{request.orange_pi_ip}:{request.orange_pi_port}/api/teleoperate/status"
            
            # 发送请求到香橙派服务
            response = await client.get(url)
            response.raise_for_status()
            
            # 返回结果
            return response.json()
    except httpx.HTTPStatusError as e:
        logging.error(f"HTTP错误 [{e.response.status_code}]: {e.response.text}")
        try:
            detail = e.response.json()
        except:
            detail = {"detail": e.response.text or "未知错误"}
        raise HTTPException(status_code=e.response.status_code, detail=detail)
    except httpx.RequestError as e:
        logging.error(f"请求错误: {str(e)}")
        raise HTTPException(status_code=503, detail=f"无法连接到遥控服务: {str(e)}")
    except Exception as e:
        logging.error(f"获取遥控状态时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取遥控状态失败: {str(e)}")

@router.post("/health")
async def check_teleoperation_health(request: TeleoperationStatusRequest):
    """检查遥控服务健康状态"""
    if not request.orange_pi_ip:
        raise HTTPException(status_code=400, detail="缺少必要参数: orange_pi_ip")
    
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            # 构建请求URL
            url = f"http://{request.orange_pi_ip}:{request.orange_pi_port}/health"
            
            # 发送请求到香橙派服务
            response = await client.get(url)
            response.raise_for_status()
            
            # 返回结果
            return response.json()
    except httpx.HTTPStatusError as e:
        logging.error(f"HTTP错误 [{e.response.status_code}]: {e.response.text}")
        try:
            detail = e.response.json()
        except:
            detail = {"detail": e.response.text or "未知错误"}
        raise HTTPException(status_code=e.response.status_code, detail=detail)
    except httpx.RequestError as e:
        logging.error(f"请求错误: {str(e)}")
        raise HTTPException(status_code=503, detail=f"无法连接到遥控服务: {str(e)}")
    except Exception as e:
        logging.error(f"检查遥控服务健康状态时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"检查遥控服务健康状态失败: {str(e)}") 
