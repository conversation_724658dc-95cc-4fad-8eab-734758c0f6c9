#!/usr/bin/env python
# 完整的Web服务器

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from fastapi import FastAPI
from fastapi.responses import HTMLResponse
import uvicorn

# 创建FastAPI应用
app = FastAPI(title="AI机械臂控制系统", version="1.0.0")

print("🚀 正在启动AI机械臂控制系统...")

try:
    # 导入Web界面
    from api.web_interface import router as web_router
    app.include_router(web_router, prefix="/web", tags=["web"])
    print("✅ Web界面模块加载成功")
except Exception as e:
    print(f"⚠️  Web界面模块加载失败: {e}")
    print("将使用基础界面")

# 添加根路径
@app.get("/")
async def root():
    return HTMLResponse("""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>AI机械臂控制系统</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
            }
            .container {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 15px;
                padding: 30px;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
            h1 {
                text-align: center;
                margin-bottom: 30px;
                font-size: 2.5em;
            }
            .status {
                background: rgba(0, 255, 0, 0.2);
                border: 1px solid rgba(0, 255, 0, 0.5);
                border-radius: 10px;
                padding: 15px;
                margin: 20px 0;
                text-align: center;
            }
            .links {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin-top: 30px;
            }
            .link-card {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
                padding: 20px;
                text-align: center;
                transition: transform 0.3s ease;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            .link-card:hover {
                transform: translateY(-5px);
                background: rgba(255, 255, 255, 0.2);
            }
            .link-card a {
                color: white;
                text-decoration: none;
                font-weight: bold;
                font-size: 1.1em;
            }
            .link-card p {
                margin-top: 10px;
                opacity: 0.8;
                font-size: 0.9em;
            }
            .features {
                margin-top: 30px;
            }
            .features ul {
                list-style: none;
                padding: 0;
            }
            .features li {
                background: rgba(255, 255, 255, 0.1);
                margin: 10px 0;
                padding: 10px 15px;
                border-radius: 5px;
                border-left: 4px solid #4CAF50;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 AI机械臂控制系统</h1>
            
            <div class="status">
                ✅ 系统已启动成功！
            </div>
            
            <div class="links">
                <div class="link-card">
                    <a href="/web/">🖥️ Web控制界面</a>
                    <p>上传图像，与AI交流，控制机械臂</p>
                </div>
                
                <div class="link-card">
                    <a href="/docs">📚 API文档</a>
                    <p>查看完整的API接口文档</p>
                </div>
                
                <div class="link-card">
                    <a href="/web/status">📊 系统状态</a>
                    <p>检查AI连接和机械臂状态</p>
                </div>
            </div>
            
            <div class="features">
                <h3>🚀 系统功能</h3>
                <ul>
                    <li>✅ 豆包1.6 AI集成 - 智能图像理解和任务规划</li>
                    <li>✅ 图像处理 - 自动压缩和格式转换</li>
                    <li>✅ 多轮对话 - 支持连续的任务交互</li>
                    <li>✅ 机械臂控制 - 基于AI输出的精确控制</li>
                    <li>✅ 实时监控 - 系统状态和执行日志</li>
                    <li>✅ 安全保护 - 紧急停止和参数验证</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin-top: 30px; opacity: 0.7;">
                <p>🔧 开发版本 v1.0.0 | 基于FastAPI + 豆包AI</p>
            </div>
        </div>
        
        <script>
            // 检查Web界面是否可用
            fetch('/web/status')
                .then(response => response.json())
                .then(data => {
                    console.log('系统状态:', data);
                })
                .catch(error => {
                    console.log('Web界面检查:', error);
                });
        </script>
    </body>
    </html>
    """)

if __name__ == "__main__":
    print("=" * 50)
    print("🌐 服务器地址: http://localhost:8088")
    print("🖥️  Web界面: http://localhost:8088/web/")
    print("📚 API文档: http://localhost:8088/docs")
    print("=" * 50)
    print("按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8088,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()
