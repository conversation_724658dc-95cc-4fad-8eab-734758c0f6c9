#!/usr/bin/env python
# AI机械臂控制器
# 基于提示词输出解析JSON并转换为机械臂控制命令

import json
import time
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
import threading
from .doubao_client import DoubaoClient
from .robot_arm import RobotArmClient

class WorkflowStage(Enum):
    """工作流程阶段"""
    PLAN = "PLAN"
    ANALYZE = "ANALYZE"
    DEBUG = "DEBUG"
    NEXT_STEP = "NEXT_STEP"
    COMPLETE = "COMPLETE"

class AIRobotController:
    """AI机械臂控制器"""
    
    def __init__(self, robot_client: RobotArmClient = None, doubao_client: DoubaoClient = None):
        """初始化控制器
        
        Args:
            robot_client: 机械臂客户端
            doubao_client: 豆包AI客户端
        """
        self.robot_client = robot_client
        self.doubao_client = doubao_client or DoubaoClient()
        
        # 当前任务状态
        self.current_task = None
        self.current_stage = None
        self.task_state = {
            "current_step_index": 0,
            "cumulative_experience": []
        }
        
        # 执行状态
        self.is_executing = False
        self.execution_lock = threading.Lock()
        
        # 命令映射
        self.command_mapping = {
            'w': {'type': 'move', 'direction': 'forward'},   # 前进+X
            's': {'type': 'move', 'direction': 'backward'},  # 后退-X
            'a': {'type': 'move', 'direction': 'right'},     # 右移+Y
            'd': {'type': 'move', 'direction': 'left'},      # 左移-Y
            'r': {'type': 'move', 'direction': 'up'},        # 上升+Z
            'f': {'type': 'move', 'direction': 'down'},      # 下降-Z
            'q': {'type': 'rotate', 'axis': 'roll', 'direction': '+'},   # Roll+
            'e': {'type': 'rotate', 'axis': 'roll', 'direction': '-'},   # Roll-
            'g': {'type': 'rotate', 'axis': 'pitch', 'direction': '+'},  # Pitch+
            't': {'type': 'rotate', 'axis': 'pitch', 'direction': '-'},  # Pitch-
            'z': {'type': 'gripper', 'action': 'close'},     # 夹爪闭合
            'c': {'type': 'gripper', 'action': 'open'},      # 夹爪张开
            '0': {'type': 'reset', 'action': 'home'}         # 复位
        }
    
    def set_robot_client(self, robot_client: RobotArmClient):
        """设置机械臂客户端"""
        self.robot_client = robot_client
    
    def parse_ai_response(self, response: str) -> Tuple[WorkflowStage, Dict[str, Any]]:
        """解析AI响应
        
        Args:
            response: AI响应文本
            
        Returns:
            (工作流程阶段, 解析的JSON数据)
        """
        try:
            # 提取工作流程标识符
            stage = None
            for line in response.split('\n'):
                line = line.strip()
                if line.startswith('#'):
                    stage_name = line[1:].strip()
                    try:
                        stage = WorkflowStage(stage_name)
                        break
                    except ValueError:
                        continue
            
            if not stage:
                raise ValueError("未找到有效的工作流程标识符")
            
            # 提取JSON数据
            json_data = self.doubao_client.extract_json_from_response(response)
            if not json_data:
                raise ValueError("未找到有效的JSON数据")
            
            return stage, json_data
            
        except Exception as e:
            raise ValueError(f"AI响应解析失败: {e}")
    
    def validate_command_structure(self, commands: List[Dict[str, Any]]) -> bool:
        """验证命令结构
        
        Args:
            commands: 命令列表
            
        Returns:
            是否有效
        """
        required_fields = ['command', 'reasoning']
        
        for cmd in commands:
            # 检查必需字段
            for field in required_fields:
                if field not in cmd:
                    return False
            
            # 检查命令是否有效
            if cmd['command'] not in self.command_mapping:
                return False
            
            # 检查参数范围
            if 'speed_percent' in cmd:
                if not (1 <= cmd['speed_percent'] <= 100):
                    return False
            
            if 'force_percent' in cmd:
                if not (1 <= cmd['force_percent'] <= 100):
                    return False
        
        return True
    
    async def execute_commands(self, commands: List[Dict[str, Any]]) -> Tuple[bool, str]:
        """执行命令列表
        
        Args:
            commands: 命令列表
            
        Returns:
            (是否成功, 执行结果消息)
        """
        if not self.robot_client:
            return False, "机械臂客户端未连接"
        
        if not self.robot_client.running:
            return False, "机械臂控制未启动"
        
        with self.execution_lock:
            self.is_executing = True
            
            try:
                executed_commands = []
                
                for i, cmd in enumerate(commands):
                    # 验证命令
                    if not self.validate_command_structure([cmd]):
                        return False, f"命令 {i+1} 格式无效"
                    
                    # 执行命令
                    success, message = await self._execute_single_command(cmd)
                    executed_commands.append({
                        'command': cmd['command'],
                        'success': success,
                        'message': message
                    })
                    
                    if not success:
                        return False, f"命令 {i+1} 执行失败: {message}"
                    
                    # 命令间延迟
                    if i < len(commands) - 1:
                        await asyncio.sleep(0.1)
                
                return True, f"成功执行 {len(commands)} 个命令"
                
            finally:
                self.is_executing = False
    
    async def _execute_single_command(self, cmd: Dict[str, Any]) -> Tuple[bool, str]:
        """执行单个命令
        
        Args:
            cmd: 命令字典
            
        Returns:
            (是否成功, 执行消息)
        """
        try:
            command = cmd['command']
            duration = cmd.get('duration', None)
            
            # 发送按键按下命令
            success, message = self.robot_client.handle_key_command(command)
            if not success:
                return False, message
            
            # 如果指定了持续时间，等待后发送按键释放命令
            if duration and duration > 0:
                await asyncio.sleep(duration)
                stop_command = f"{command}_stop"
                success, stop_message = self.robot_client.handle_key_command(stop_command)
                if not success:
                    return False, f"停止命令失败: {stop_message}"
                
                return True, f"执行命令 {command} 持续 {duration}s"
            else:
                # 对于瞬时命令（如复位），直接返回成功
                if command in ['0', 'p']:
                    return True, f"执行命令 {command}"
                else:
                    # 对于持续性命令，需要手动停止
                    return True, f"开始执行命令 {command}（持续性）"
                    
        except Exception as e:
            return False, f"命令执行异常: {e}"
    
    async def stop_all_commands(self) -> bool:
        """停止所有正在执行的命令
        
        Returns:
            是否成功
        """
        if not self.robot_client:
            return False
        
        try:
            # 发送所有可能的停止命令
            stop_commands = ['w_stop', 's_stop', 'a_stop', 'd_stop', 'r_stop', 'f_stop',
                           'q_stop', 'e_stop', 'g_stop', 't_stop', 'z_stop', 'c_stop']
            
            for stop_cmd in stop_commands:
                self.robot_client.handle_key_command(stop_cmd)
                await asyncio.sleep(0.01)  # 短暂延迟
            
            return True
            
        except Exception as e:
            print(f"停止命令失败: {e}")
            return False
    
    async def process_task_with_image(self, query: str, image_path: str) -> Dict[str, Any]:
        """处理带图像的任务
        
        Args:
            query: 任务查询
            image_path: 图像路径
            
        Returns:
            处理结果
        """
        try:
            # 发送消息给AI
            response = self.doubao_client.send_message(query, image_path)
            
            # 解析响应
            stage, json_data = self.parse_ai_response(response)
            
            # 更新任务状态
            self.current_stage = stage
            if 'state' in json_data:
                self.task_state.update(json_data['state'])
            
            result = {
                'stage': stage.value,
                'response': response,
                'parsed_data': json_data,
                'task_state': self.task_state.copy()
            }
            
            # 如果是规划阶段或调试阶段，执行命令
            if stage in [WorkflowStage.PLAN, WorkflowStage.DEBUG, WorkflowStage.NEXT_STEP]:
                if 'steps' in json_data:
                    # 查找当前要执行的步骤
                    current_step = None
                    for step in json_data['steps']:
                        # 查找标记为当前执行的步骤（包含[*]）
                        for key in step.keys():
                            if key.startswith('[*]') and 'commands' in step:
                                current_step = step
                                break
                        if current_step:
                            break
                    
                    if current_step and 'commands' in current_step:
                        # 执行命令
                        success, message = await self.execute_commands(current_step['commands'])
                        result['execution'] = {
                            'success': success,
                            'message': message,
                            'commands_executed': len(current_step['commands'])
                        }
            
            return result
            
        except Exception as e:
            return {
                'stage': 'ERROR',
                'error': str(e),
                'task_state': self.task_state.copy()
            }
    
    async def process_feedback_image(self, image_path: str) -> Dict[str, Any]:
        """处理反馈图像
        
        Args:
            image_path: 反馈图像路径
            
        Returns:
            分析结果
        """
        try:
            # 发送反馈图像给AI进行分析
            response = self.doubao_client.send_message("请分析执行结果", image_path)
            
            # 解析响应
            stage, json_data = self.parse_ai_response(response)
            
            # 更新任务状态
            self.current_stage = stage
            if 'state' in json_data:
                self.task_state.update(json_data['state'])
            
            result = {
                'stage': stage.value,
                'response': response,
                'parsed_data': json_data,
                'task_state': self.task_state.copy()
            }
            
            # 根据分析结果决定下一步行动
            if stage == WorkflowStage.ANALYZE:
                status = json_data.get('status', 'UNKNOWN')
                if status == 'FAILURE':
                    # 如果失败，停止所有命令
                    await self.stop_all_commands()
                    result['action'] = 'stopped_due_to_failure'
                elif status == 'SUCCESS':
                    result['action'] = 'ready_for_next_step'
            
            return result
            
        except Exception as e:
            return {
                'stage': 'ERROR',
                'error': str(e),
                'task_state': self.task_state.copy()
            }
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前状态
        
        Returns:
            当前状态信息
        """
        return {
            'current_stage': self.current_stage.value if self.current_stage else None,
            'task_state': self.task_state.copy(),
            'is_executing': self.is_executing,
            'robot_connected': self.robot_client is not None and hasattr(self.robot_client, 'running'),
            'robot_running': self.robot_client.running if self.robot_client else False
        }
    
    def reset_task(self):
        """重置任务状态"""
        self.current_task = None
        self.current_stage = None
        self.task_state = {
            "current_step_index": 0,
            "cumulative_experience": []
        }
        self.doubao_client.clear_conversation()
