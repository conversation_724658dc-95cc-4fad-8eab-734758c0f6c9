from fastapi import Fast<PERSON><PERSON>, WebSocket, WebSocketDisconnect, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
# from fastapi.staticfiles import StaticFiles # 如果不用静态文件可以注释掉
from fastapi.responses import JSONResponse, StreamingResponse

import uvicorn
# import asyncio # 如果不用可以注释掉
import os
# from typing import List, Dict # 如果不用可以注释掉
# import time # 如果不用可以注释掉
import httpx # <--- 新增导入

# 导入API模块 - 注意：移除了 action_replay
from api import control, dataset, config, camera, docs, robot_arm, wheel, chess_game, config as config_api, multimodal, teleoperate, web_interface
# 创建FastAPI应用
app = FastAPI(title="数派智能机械臂控制平台API")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# 注册路由 - 注意：移除了 action_replay.router
app.include_router(control.router, prefix="/control", tags=["control"])
app.include_router(dataset.router, prefix="/dataset", tags=["dataset"])
app.include_router(config.router, prefix="/config", tags=["config"])
app.include_router(camera.router, prefix="/camera", tags=["camera"])
app.include_router(docs.router, prefix="/docs", tags=["docs"])
app.include_router(robot_arm.router, prefix="/robot", tags=["robot"])
app.include_router(wheel.router, prefix="/wheel", tags=["wheel"])
app.include_router(chess_game.router, prefix="/api/chess", tags=["chess"])
app.include_router(config_api.router, prefix="/api/config", tags=["配置"])
# app.include_router(action_replay.router, prefix="/action", tags=["action_replay"]) # <--- 已移除
app.include_router(multimodal.router, prefix="/multimodal", tags=["multimodal"])
# 添加新的遥控路由
app.include_router(teleoperate.router, prefix="/teleoperate", tags=["teleoperate"])
# 添加Web界面路由
app.include_router(web_interface.router, prefix="/web", tags=["web"])

# --- 新增：香橙派 Action Replay 服务代理 ---
ORANGE_PI_ACTION_REPLAY_BASE_URL = "http://192.168.0.89:8002/api/action_replay"

# 通用代理函数，可以处理 GET, POST, DELETE 等多种方法，支持自定义目标URL
async def proxy_request_to_custom_url(request: Request, path: str, base_url: str):
    async with httpx.AsyncClient() as client:
        # 构建目标 URL
        url = f"{base_url}{path}"

        # 准备请求数据和头部
        data = None
        json_data = None # 显式初始化json_data
        content_data = None # 显式初始化content_data

        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                # 尝试读取为JSON
                json_data = await request.json()
                print(f"Proxying JSON data: {json_data}")
            except Exception:
                # 如果不是JSON，则读取为原始字节
                content_data = await request.body()
                print(f"Proxying raw body data, length: {len(content_data) if content_data else 0}")

        # 复制请求头，特别是 Content-Type 等
        headers = {key: value for key, value in request.headers.items() if key.lower() not in ['host', 'connection', 'user-agent', 'accept-encoding', 'content-length']}
        # 如果发送的是json_data,确保Content-Type是application/json
        if json_data is not None and 'content-type' not in (h.lower() for h in headers.keys()):
            headers['Content-Type'] = 'application/json'
        elif content_data is not None and 'content-type' not in (h.lower() for h in headers.keys()):
            # 如果发送的是原始字节且没有Content-Type，可以考虑从原始请求中获取或设置一个默认值
            # headers['Content-Type'] = request.headers.get('content-type', 'application/octet-stream') # Example
            pass


        try:
            # 发起请求
            print(f"Proxying {request.method} to {url}")
            if json_data is not None:
                print(f"Sending JSON: {json_data}")
                response = await client.request(
                    method=request.method, url=url, json=json_data, params=request.query_params, headers=headers, timeout=30.0
                )
            elif content_data is not None:
                print(f"Sending content (bytes), length: {len(content_data)}")
                response = await client.request(
                    method=request.method, url=url, content=content_data, params=request.query_params, headers=headers, timeout=30.0
                )
            else: # For GET, DELETE etc. that might not have a body
                response = await client.request(
                    method=request.method, url=url, params=request.query_params, headers=headers, timeout=30.0
                )

            response.raise_for_status()

            if 'application/octet-stream' in response.headers.get('content-type', '').lower():
                 return StreamingResponse(response.aiter_bytes(), media_type=response.headers['content-type'])

            return JSONResponse(content=response.json(), status_code=response.status_code)

        except httpx.HTTPStatusError as e:
            print(f"HTTPStatusError while proxying to custom URL: {e.response.status_code} - {e.response.text}")
            detail_content = e.response.text
            try:
                detail_content = e.response.json()
            except:
                pass # Keep as text if not json
            raise HTTPException(status_code=e.response.status_code, detail=detail_content)
        except httpx.RequestError as e:
            print(f"RequestError while proxying to custom URL: {str(e)}")
            raise HTTPException(status_code=503, detail=f"Service unavailable: Error connecting to server - {str(e)}")
        except Exception as e:
            print(f"Generic error while proxying: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Internal server error during proxy: {str(e)}")

# 使用默认香橙派URL的代理函数
async def proxy_request_to_orange_pi(request: Request, path: str):
    return await proxy_request_to_custom_url(request, path, ORANGE_PI_ACTION_REPLAY_BASE_URL)

@app.post("/action/connect")
async def proxy_connect(request: Request):
    # 从请求中获取香橙派的IP地址和端口，如果存在
    try:
        data = await request.json()
        if 'orange_pi_ip' in data and 'orange_pi_port' in data:
            # 动态构建香橙派URL
            orange_pi_url = f"http://{data['orange_pi_ip']}:{data['orange_pi_port']}/api/action_replay"
            # 使用自定义URL进行代理
            return await proxy_request_to_custom_url(request, "/connect", orange_pi_url)
    except Exception as e:
        print(f"Error parsing connect request: {str(e)}")

    # 如果没有提供自定义URL或出错，使用默认URL
    return await proxy_request_to_orange_pi(request, "/connect")

@app.post("/action/disconnect")
async def proxy_disconnect(request: Request):
    # 从请求中获取香橙派的IP地址和端口，如果存在
    try:
        data = await request.json()
        if 'orange_pi_ip' in data and 'orange_pi_port' in data:
            # 动态构建香橙派URL
            orange_pi_url = f"http://{data['orange_pi_ip']}:{data['orange_pi_port']}/api/action_replay"
            # 使用自定义URL进行代理
            return await proxy_request_to_custom_url(request, "/disconnect", orange_pi_url)
    except Exception as e:
        print(f"Error parsing disconnect request: {str(e)}")

    # 如果没有提供自定义URL或出错，使用默认URL
    return await proxy_request_to_orange_pi(request, "/disconnect")

@app.post("/action/record")
async def proxy_record(request: Request):
    # 从请求中获取香橙派的IP地址和端口，如果存在
    try:
        data = await request.json()
        if 'orange_pi_ip' in data and 'orange_pi_port' in data:
            # 动态构建香橙派URL
            orange_pi_url = f"http://{data['orange_pi_ip']}:{data['orange_pi_port']}/api/action_replay"
            print(f"使用动态URL录制动作: {orange_pi_url}")
            # 使用自定义URL进行代理
            return await proxy_request_to_custom_url(request, "/record", orange_pi_url)
    except Exception as e:
        print(f"Error parsing record request: {str(e)}")

    # 如果没有提供自定义URL或出错，使用默认URL
    print(f"使用默认URL录制动作: {ORANGE_PI_ACTION_REPLAY_BASE_URL}")
    return await proxy_request_to_orange_pi(request, "/record")

@app.post("/action/play")
async def proxy_play(request: Request):
    # 从请求中获取香橙派的IP地址和端口，如果存在
    try:
        data = await request.json()
        if 'orange_pi_ip' in data and 'orange_pi_port' in data:
            # 动态构建香橙派URL
            orange_pi_url = f"http://{data['orange_pi_ip']}:{data['orange_pi_port']}/api/action_replay"
            print(f"使用动态URL播放动作: {orange_pi_url}")
            # 使用自定义URL进行代理
            return await proxy_request_to_custom_url(request, "/play", orange_pi_url)
    except Exception as e:
        print(f"Error parsing play request: {str(e)}")

    # 如果没有提供自定义URL或出错，使用默认URL
    print(f"使用默认URL播放动作: {ORANGE_PI_ACTION_REPLAY_BASE_URL}")
    return await proxy_request_to_orange_pi(request, "/play")

@app.get("/action/list")
async def proxy_list_actions(request: Request):
    # 从查询参数中获取香橙派的IP地址和端口，如果存在
    try:
        orange_pi_ip = request.query_params.get('orange_pi_ip')
        orange_pi_port = request.query_params.get('orange_pi_port')
        
        if orange_pi_ip and orange_pi_port:
            # 动态构建香橙派URL
            orange_pi_url = f"http://{orange_pi_ip}:{orange_pi_port}/api/action_replay"
            print(f"使用动态URL获取动作列表: {orange_pi_url}")
            # 使用自定义URL进行代理
            return await proxy_request_to_custom_url(request, "/list", orange_pi_url)
    except Exception as e:
        print(f"Error parsing list request query params: {str(e)}")

    # 如果没有提供自定义URL或出错，使用默认URL
    print(f"使用默认URL获取动作列表: {ORANGE_PI_ACTION_REPLAY_BASE_URL}")
    return await proxy_request_to_orange_pi(request, "/list")

@app.delete("/action/delete/{record_name}")
async def proxy_delete_action(record_name: str, request: Request):
    # 从查询参数中获取香橙派的IP地址和端口，如果存在
    try:
        orange_pi_ip = request.query_params.get('orange_pi_ip')
        orange_pi_port = request.query_params.get('orange_pi_port')
        
        if orange_pi_ip and orange_pi_port:
            # 动态构建香橙派URL
            orange_pi_url = f"http://{orange_pi_ip}:{orange_pi_port}/api/action_replay"
            print(f"使用动态URL删除动作: {orange_pi_url}")
            # 使用自定义URL进行代理
            return await proxy_request_to_custom_url(request, f"/delete/{record_name}", orange_pi_url)
    except Exception as e:
        print(f"Error parsing delete request query params: {str(e)}")

    # 如果没有提供自定义URL或出错，使用默认URL
    print(f"使用默认URL删除动作: {ORANGE_PI_ACTION_REPLAY_BASE_URL}")
    return await proxy_request_to_orange_pi(request, f"/delete/{record_name}")

# WebSocket连接管理 (这部分保持不变)
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {
            "camera": [],
            "control": [],
            "wheel": [],  # 添加wheel类型的WebSocket连接
            "chess": [],  # 添加棋盘游戏的WebSocket连接
            "multimodal": []  # 添加多模态助手的WebSocket连接
        }

    async def connect(self, websocket: WebSocket, client_type: str):
        await websocket.accept()
        if client_type in self.active_connections:
            self.active_connections[client_type].append(websocket)

    def disconnect(self, websocket: WebSocket, client_type: str):
        if client_type in self.active_connections:
            if websocket in self.active_connections[client_type]:
                self.active_connections[client_type].remove(websocket)

    async def broadcast(self, message: dict, client_type: str):
        if client_type in self.active_connections:
            for connection in self.active_connections[client_type]:
                await connection.send_json(message)

manager = ConnectionManager()

# WebSocket路由
@app.websocket("/ws/{client_type}")
async def websocket_endpoint(websocket: WebSocket, client_type: str):
    print(f"新的WebSocket连接: {client_type}")
    await manager.connect(websocket, client_type)
    try:
        while True:
            # 接收并处理WebSocket消息
            data = await websocket.receive_json()
            print(f"收到{client_type}消息: {data.get('type', 'unknown')}")

            # 根据客户端类型处理不同的消息
            if client_type == "control":
                # 处理控制命令
                await manager.broadcast({"type": "control_update", "data": data}, "control")
            elif client_type == "camera":
                # 处理摄像头数据
                await manager.broadcast({"type": "camera_frame", "data": data}, "camera")
            elif client_type == "wheel":
                # 处理万向轮数据
                await manager.broadcast({"type": "wheel_update", "data": data}, "wheel")
            elif client_type == "chess":
                # 处理棋盘游戏的消息
                await manager.broadcast({"type": "chess_update", "data": data}, "chess")
            elif client_type == "multimodal":
                # 处理多模态消息
                print(f"转发多模态消息: {data.get('type', 'unknown')}")
                await manager.broadcast({"type": "multimodal_update", "data": data}, "multimodal")
    except WebSocketDisconnect:
        print(f"WebSocket断开连接: {client_type}")
        manager.disconnect(websocket, client_type)
    except Exception as e:
        print(f"WebSocket错误({client_type}): {e}")
        manager.disconnect(websocket, client_type)

# 从wheel API模块获取websocket处理函数的直接引用
# Make sure 'wheel' module and its router are correctly defined and imported
if hasattr(wheel, 'router') and wheel.router.routes: # Check if router and routes exist
    # Attempt to find a WebSocket route; this is a guess and might need adjustment
    # based on how wheel.router is structured.
    ws_route = next((route for route in wheel.router.routes if isinstance(getattr(route, "endpoint", None), type(websocket_endpoint))), None)
    if ws_route:
        wheel_websocket_handler = ws_route.endpoint
    else:
        wheel_websocket_handler = None # Or some default handler/error
        print("Warning: Could not find WebSocket endpoint in wheel.router")
else:
    wheel_websocket_handler = None
    print("Warning: wheel.router is not available or has no routes.")


# 健康检查端点
@app.get("/health")
async def health_check():
    return {"status": "healthy"}

# 启动服务器
if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8088, reload=True)

