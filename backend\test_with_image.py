#!/usr/bin/env python
# 使用提供的图像测试系统

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from api.doubao_client import DoubaoClient
from api.image_processor import ImageProcessor
from api.ai_robot_controller import AIRobotController

async def test_with_provided_image():
    """使用提供的图像测试系统"""
    print("🖼️  使用提供的图像测试AI机械臂控制系统")
    print("=" * 60)
    
    # 图像路径
    image_path = Path(__file__).parent / "api接入" / "image.png"
    
    if not image_path.exists():
        print("❌ 图像文件不存在:", image_path)
        return False
    
    print(f"📁 图像路径: {image_path}")
    
    try:
        # 1. 验证图像
        print("\n1. 验证图像...")
        is_valid, message = ImageProcessor.validate_image(image_path)
        if is_valid:
            print("✅ 图像验证成功")
            
            # 获取图像信息
            info = ImageProcessor.get_image_info(image_path)
            print(f"   尺寸: {info['width']}x{info['height']}")
            print(f"   格式: {info['format']}")
            print(f"   大小: {info['size_bytes']} bytes")
        else:
            print(f"❌ 图像验证失败: {message}")
            return False
        
        # 2. 测试图像压缩
        print("\n2. 测试图像压缩...")
        base64_str = ImageProcessor.image_to_base64(image_path, max_size=1024)
        print(f"✅ 图像压缩成功，base64长度: {len(base64_str)}")
        
        # 3. 创建AI客户端
        print("\n3. 创建AI客户端...")
        doubao_client = DoubaoClient()
        print("✅ AI客户端创建成功")
        
        # 4. 测试简单对话
        print("\n4. 测试简单对话...")
        response = doubao_client.send_message("你好，请简单回复'测试成功'")
        print(f"✅ 简单对话成功: {response[:50]}...")
        
        # 5. 测试图像理解
        print("\n5. 测试图像理解...")
        image_response = doubao_client.send_message(
            "请描述这张图像中的内容，特别是机械臂相关的元素", 
            str(image_path)
        )
        print("✅ 图像理解测试成功")
        print(f"AI对图像的描述: {image_response[:200]}...")
        
        # 6. 测试机械臂任务规划
        print("\n6. 测试机械臂任务规划...")
        task_query = "请分析这张图像，制定一个机械臂抓取任务的计划。请按照提示词格式输出JSON计划。"
        
        # 清空对话历史，重新开始
        doubao_client.clear_conversation()
        
        task_response = doubao_client.send_message(task_query, str(image_path))
        print("✅ 任务规划测试成功")
        print(f"AI任务规划响应: {task_response[:300]}...")
        
        # 7. 尝试解析AI响应
        print("\n7. 测试响应解析...")
        controller = AIRobotController(doubao_client=doubao_client)
        
        try:
            stage, json_data = controller.parse_ai_response(task_response)
            print(f"✅ 响应解析成功")
            print(f"   工作流程阶段: {stage.value}")
            if 'plan_name' in json_data:
                print(f"   计划名称: {json_data['plan_name']}")
            if 'steps' in json_data:
                print(f"   步骤数量: {len(json_data['steps'])}")
        except Exception as e:
            print(f"⚠️  响应解析失败（这是正常的，AI可能没有返回标准格式）: {e}")
            
            # 尝试提取JSON
            json_data = doubao_client.extract_json_from_response(task_response)
            if json_data:
                print(f"✅ 找到JSON数据: {list(json_data.keys())}")
            else:
                print("ℹ️  未找到JSON数据，但这不影响系统功能")
        
        # 8. 测试完整工作流程模拟
        print("\n8. 测试完整工作流程模拟...")
        
        # 使用更明确的提示
        workflow_query = """请分析这张图像并制定机械臂控制计划。

请严格按照以下格式输出：

#PLAN
```json
{
  "plan_name": "任务名称",
  "task_summary": "任务简述",
  "steps": [
    {
      "[*]step_name": "第一步名称",
      "commands": [
        {
          "command": "w",
          "duration": 1.0,
          "speed_percent": 50,
          "force_percent": 50,
          "reasoning": "移动原因和物理原理"
        }
      ]
    }
  ],
  "state": {"current_step_index": 0, "cumulative_experience": []}
}
```"""
        
        # 清空对话历史
        doubao_client.clear_conversation()
        
        workflow_response = doubao_client.send_message(workflow_query, str(image_path))
        print("✅ 工作流程测试完成")
        
        # 保存响应到文件以便查看
        with open("ai_response.txt", "w", encoding="utf-8") as f:
            f.write("=== 图像理解响应 ===\n")
            f.write(image_response)
            f.write("\n\n=== 任务规划响应 ===\n")
            f.write(task_response)
            f.write("\n\n=== 工作流程响应 ===\n")
            f.write(workflow_response)
        
        print(f"📄 AI响应已保存到: ai_response.txt")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

async def main():
    """主函数"""
    success = await test_with_provided_image()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 图像测试完成！")
        print("\n📋 下一步操作:")
        print("1. 查看 ai_response.txt 文件了解AI的响应")
        print("2. 访问 http://localhost:8088/web/ 使用Web界面")
        print("3. 在Web界面中上传图像并测试完整流程")
        print("4. 尝试不同的任务描述，例如：")
        print("   - '请抓取图像中的目标物体'")
        print("   - '分析当前场景并制定移动计划'")
        print("   - '移动机械臂到安全位置'")
    else:
        print("❌ 图像测试失败，请检查配置")

if __name__ == "__main__":
    asyncio.run(main())
