from fastapi import APIRouter, FastAPI, HTTPException, Body
from pydantic import BaseModel
import time
import os
import asyncio
import random  # 添加random模块用于随机选择episodes
# 香橙派
# 机械臂控制相关导入
try:
    from lerobot.common.datasets.lerobot_dataset import LeRobotDataset
    from lerobot.common.robot_devices.control_utils import log_control_info
    from lerobot.common.robot_devices.robots.utils import make_robot_from_config
    from lerobot.common.utils.utils import init_logging
    from lerobot.common.robot_devices.robots.configs import So101RobotConfig # Specific for SO101
    from lerobot.common.robot_devices.robots.configs import So100RobotConfig # Added for SO100
    LEROBOT_AVAILABLE = True
except ImportError:
    LEROBOT_AVAILABLE = False
    print("警告: LeRobot 库未安装，机械臂控制功能不可用。请确保在香橙派上正确安装 LeRobot。")

# 创建 FastAPI 应用和路由
app = FastAPI(title="机械臂控制服务 (SO100)")
router = APIRouter()

# 模型定义
class RobotConnectionRequest(BaseModel):
    port: str = "/dev/ttyACM0"  # 固定使用从臂串口
    robot_type: str = "so100"  # 默认为 SO101，现在也支持 "so100"

class MoveRequest(BaseModel):
    position: int

# 全局变量
robot = None

# ========== 初始化机械臂 (针对不同型号) ==========
def init_robot(port: str, robot_type: str):
    """根据机械臂类型选择合适的初始化函数"""
    if not LEROBOT_AVAILABLE:
        return False, "LeRobot 库未安装，无法连接机械臂"
    
    robot_type = robot_type.lower()  # 统一转为小写
    
    if robot_type == "so101":
        return init_robot_so101(port)
    elif robot_type == "so100":
        return init_robot_so100(port)
    else:
        return False, f"不支持的机械臂类型: {robot_type}。当前支持: so100, so101"

# ========== 初始化机械臂 (SO101 特定) ==========
def init_robot_so101(port: str):
    if not LEROBOT_AVAILABLE:
        return False, "LeRobot 库未安装，无法连接机械臂"
        
    global robot
    try:
        init_logging() # Initialize LeRobot logging

        print(f"为 SO101 创建配置，端口: {port}")
        robot_config = So101RobotConfig()

        # 设置串口 for SO101
        # The So101RobotConfig might have a specific way to set the port,
        # typically via follower_arms or directly if it's a simpler config.
        if hasattr(robot_config, "follower_arms") and "main" in robot_config.follower_arms:
            robot_config.follower_arms["main"].port = port
            # Leader arm port logic - adjust if necessary for your OrangePi setup
            if hasattr(robot_config, "leader_arms") and "main" in robot_config.leader_arms:
                leader_port = "/dev/ttyACM0" if port == "/dev/ttyACM1" else "/dev/ttyACM1" if port == "/dev/ttyACM0" else "COM6"
                robot_config.leader_arms["main"].port = leader_port
                print(f"设置 leader arm 端口为 {leader_port}")
        elif hasattr(robot_config, "port"): # Fallback if structure is simpler
             robot_config.port = port
        else:
            print("[WARN] So101RobotConfig 结构中未找到 'follower_arms' 或 'port' 属性来设置端口。")
            # Depending on So101RobotConfig, this might still work if port is set by default or another way
            # For now, we'll proceed, but this might need adjustment based on the exact config structure.

        print(f"正在连接机械臂 SO101 在端口 {port}...")
        robot = make_robot_from_config(robot_config)
        
        robot.connect()
        return True, f"成功连接到机械臂 SO101 在端口 {port}"
    except Exception as e:
        error_msg = f"初始化 SO101 机械臂时出错: {e}"
        print(f"详细错误: {repr(e)}")
        if "OpenCV" in str(e): # OpenCV errors often relate to camera, though less direct here
            error_msg += "\n检测到OpenCV错误，可能与 LeRobot 内部相机相关依赖有关，即使此服务不直接用相机。"
        return False, error_msg

# ========== 初始化机械臂 (SO100 特定) ==========
def init_robot_so100(port: str):
    """初始化 SO100 型号的机械臂"""
    global robot
    try:
        init_logging() # Initialize LeRobot logging

        print(f"为 SO100 创建配置，端口: {port}")
        robot_config = So100RobotConfig()

        # 设置串口 for SO100
        if hasattr(robot_config, "follower_arms") and "main" in robot_config.follower_arms:
            robot_config.follower_arms["main"].port = port
            if hasattr(robot_config, "leader_arms") and "main" in robot_config.leader_arms:
                leader_port = "/dev/ttyACM0" if port == "/dev/ttyACM1" else "/dev/ttyACM1" if port == "/dev/ttyACM0" else "COM6"
                robot_config.leader_arms["main"].port = leader_port
                print(f"设置 leader arm 端口为 {leader_port}")
        elif hasattr(robot_config, "port"):
             robot_config.port = port
        else:
            print("[WARN] So100RobotConfig 结构中未找到 'follower_arms' 或 'port' 属性来设置端口。")

        print(f"正在连接机械臂 SO100 在端口 {port}...")
        robot = make_robot_from_config(robot_config)
        
        robot.connect()
        return True, f"成功连接到机械臂 SO100 在端口 {port}"
    except Exception as e:
        error_msg = f"初始化 SO100 机械臂时出错: {e}"
        print(f"详细错误: {repr(e)}")
        if "OpenCV" in str(e):
            error_msg += "\n检测到OpenCV错误，可能与 LeRobot 内部相机相关依赖有关，即使此服务不直接用相机。"
        return False, error_msg

# ========== 通用动作执行函数 ==========
# This function is copied from chess_game.py and might need adjustments
# if it has dependencies not carried over, or if robot_instance handling changes.
async def execute_movement(repo_id, robot_instance):
    if not robot_instance:
        print("[ERROR] 机械臂实例未初始化 (execute_movement)")
        raise HTTPException(status_code=500, detail="机械臂实例未初始化")
    if not LEROBOT_AVAILABLE:
        print("[ERROR] LeRobot 库不可用 (execute_movement)")
        raise HTTPException(status_code=500, detail="LeRobot 库不可用")
    try:
        # 随机选择一个episode，可以是0、1或2
        random_episode = random.choice([0, 1, 2])
        print(f"从 repo_id 加载数据集: {repo_id}，使用随机episode: {random_episode}")
        
        # 使用随机选择的episode创建数据集
        dataset = LeRobotDataset(repo_id=repo_id, root=None, episodes=[random_episode])
        actions = dataset.hf_dataset.select_columns("action")

        total_frames = dataset.num_frames
        print(f"数据集加载完成，来自 {repo_id}，使用episode {random_episode}，共{total_frames}帧")
        
        # 设置最小帧间隔时间（秒）
        MIN_FRAME_INTERVAL = 0.05

        for idx in range(dataset.num_frames):
            start_t = time.perf_counter()

            try:
                action = actions[idx]["action"]

                if not hasattr(robot_instance, 'send_action') or not callable(getattr(robot_instance, 'send_action')):
                    print(f"[ERROR] 机械臂对象没有 send_action 方法或该方法不可调用")
                    raise HTTPException(status_code=500, detail="机械臂控制接口异常")

                try:
                    robot_instance.send_action(action)
                except Exception as action_error:
                    print(f"[ERROR] 发送动作到机械臂时出错: {action_error}")
                    await asyncio.sleep(0.1)
                    try:
                        robot_instance.send_action(action)  # Retry once
                    except Exception as retry_error:
                        print(f"[ERROR] 重试发送动作仍然失败: {retry_error}")

                if idx % 30 == 0:
                    print(f"执行进度 ({repo_id}): {idx}/{total_frames}")

            except Exception as e:
                print(f"执行第{idx}帧动作时出错 ({repo_id}): {e}")
                continue

            elapsed = time.perf_counter() - start_t
            sleep_time = max(0, MIN_FRAME_INTERVAL - elapsed)

            if sleep_time > 0:
                await asyncio.sleep(sleep_time)

            actual_elapsed = time.perf_counter() - start_t
            safe_dt = max(actual_elapsed, 1e-6)
            log_control_info(robot_instance, safe_dt, fps=1 / MIN_FRAME_INTERVAL)
    except Exception as e:
        print(f"[ERROR] 执行动作失败 ({repo_id}): {e}")
        raise HTTPException(status_code=500, detail=f"执行动作失败 ({repo_id}): {e}")

# ========== 动作映射 ==========
# These are the repo_ids for different chess moves.
# Copied from chess_game.py. Ensure these repos are accessible by LeRobot on Orange Pi.
position_moves = {
    0: "angle2001/replay_left_top",
    1: "angle2001/replay_med_top",
    2: "angle2001/replay_right_top",
    3: "angle2001/replay_left_med",
    4: "angle2001/replay_med_med",
    5: "angle2001/replay_right_med",
    6: "angle2001/replay_left_down",
    7: "angle2001/replay_med_down",
    8: "angle2001/replay_right_down",
}
VICTORY_REPO_ID = "angle2001/replay_victory"

# ========== API 路由 ==========

@router.post("/connect")
async def connect_robot_endpoint(conn_request: RobotConnectionRequest):
    """连接机械臂接口，支持 SO100 和 SO101 型号"""
    global robot
    if robot is not None:
        try:
            # Attempt to disconnect if already connected, to ensure clean state
            if hasattr(robot, 'is_connected') and callable(getattr(robot, 'is_connected')) and robot.is_connected():
                print("机械臂已连接，尝试断开旧连接...")
                if hasattr(robot, 'disconnect') and callable(getattr(robot, 'disconnect')):
                    robot.disconnect()
                robot = None
                print("旧连接已断开。")
            elif not (hasattr(robot, 'is_connected') and callable(getattr(robot, 'is_connected'))):
                 print("机械臂已存在但无法检查连接状态，重置实例...")
                 robot = None # Reset if state is unknown
        except Exception as e:
            print(f"断开旧连接时出错: {e}")
            robot = None # Reset on error

    success, message = init_robot(conn_request.port, conn_request.robot_type)
    if success:
        return {"status": "success", "message": message}
    else:
        # Ensure robot is None if connection failed
        robot = None
        raise HTTPException(status_code=500, detail=message)

@router.post("/move")
async def move_robot_endpoint(move_request: MoveRequest):
    """执行机械臂落子动作接口"""
    global robot
    if robot is None:
        raise HTTPException(status_code=400, detail="机械臂未连接，请先调用 /connect")

    # 检查机械臂连接状态
    try:
        if hasattr(robot, 'is_connected') and callable(getattr(robot, 'is_connected')):
            if not robot.is_connected():
                print("[ERROR] 机械臂已断开连接 (move endpoint)")
                # Attempt to reconnect? Or just fail. For now, fail.
                robot = None # Clear stale robot instance
                raise HTTPException(status_code=400, detail="机械臂已断开连接，请重新连接")
    except Exception as e:
        print(f"[WARN] 检查机械臂连接状态时出错: {e}. 尝试继续...")

    position = move_request.position
    repo_id = position_moves.get(position)
    if not repo_id:
        raise HTTPException(status_code=400, detail=f"无效位置: {position}")
        
    print(f"[INFO] 服务端: 请求执行动作至位置 {position} (数据集: {repo_id})")
    try:
        await execute_movement(repo_id, robot)
        return {"status": "success", "message": f"在位置 {position} 动作序列开始执行"}
    except HTTPException as http_exc: # Re-raise HTTP exceptions from execute_movement
        raise http_exc
    except Exception as e:
        print(f"[ERROR] 执行动作时出现未处理异常 (位置 {position}): {e}")
        # Provide more context if possible
        raise HTTPException(status_code=500, detail=f"在位置 {position} 执行动作时服务端出现错误: {str(e)}")

@router.post("/victory")
async def victory_dance_endpoint():
    """执行胜利动作接口"""
    global robot
    if robot is None:
        raise HTTPException(status_code=400, detail="机械臂未连接，请先调用 /connect")

    # 检查机械臂连接状态
    try:
        if hasattr(robot, 'is_connected') and callable(getattr(robot, 'is_connected')):
            if not robot.is_connected():
                print("[ERROR] 机械臂已断开连接 (victory endpoint)")
                robot = None # Clear stale robot instance
                raise HTTPException(status_code=400, detail="机械臂已断开连接，请重新连接")
    except Exception as e:
        print(f"[WARN] 检查机械臂连接状态时出错: {e}. 尝试继续...")
        
    # 不再执行胜利动作，只记录日志并返回成功响应
    print("🎉 服务端: 收到胜利动作请求，但根据需求配置不执行实际动作")
    
    return {"status": "success", "message": "根据需求配置，不执行胜利动作，仅在前端显示胜利信息"}

@router.post("/disconnect")
async def disconnect_robot_endpoint():
    """断开机械臂连接接口"""
    global robot
    if robot is None:
        return {"status": "success", "message": "机械臂已是断开状态"}
    
    try:
        if hasattr(robot, 'disconnect') and callable(getattr(robot, 'disconnect')):
            print("正在断开机械臂连接...")
            robot.disconnect()
        robot = None
        return {"status": "success", "message": "已成功断开机械臂连接"}
    except Exception as e:
        print(f"[ERROR] 断开机械臂连接时出错: {e}")
        # Even if disconnect fails, set robot to None to allow re-connection attempt
        robot = None
        raise HTTPException(status_code=500, detail=f"断开连接失败: {str(e)}")

@router.get("/health")
async def health_check_endpoint():
    """健康检查接口"""
    global robot
    connected = False
    if robot and hasattr(robot, 'is_connected') and callable(getattr(robot, 'is_connected')):
        try:
            connected = robot.is_connected()
        except Exception as e:
            print(f"健康检查: 检查连接状态时出错 - {e}")
            connected = False # Assume not connected if check fails
    elif robot: # If robot object exists but no is_connected method, assume connected if object is there
        connected = True

    return {
        "status": "ok",
        "lerobot_available": LEROBOT_AVAILABLE,
        "robot_instance_exists": robot is not None,
        "robot_connected": connected
    }

app.include_router(router, prefix="/api/arm") # Prefix all arm routes

if __name__ == "__main__":
    import uvicorn
    # 提醒用户根据实际情况修改 host 和 port
    print("启动机械臂控制服务 (SO100)...")
    print("请确保 LeRobot 相关依赖已在环境中正确安装。")
    print("您可能需要修改以下 uvicorn.run 中的 host 地址以允许网络访问，例如 '0.0.0.0'。")
    uvicorn.run(app, host="0.0.0.0", port=8001) # For OrangePi, use '0.0.0.0' to be accessible 