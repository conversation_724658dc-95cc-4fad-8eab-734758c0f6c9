#!/usr/bin/env python
# 豆包1.6 API客户端模块
# 实现多轮对话、图像处理、返回值预处理等功能

import json
import re
import base64
import requests
from typing import List, Dict, Any, Optional, Tuple
from PIL import Image
import io
import os
from pathlib import Path

class DoubaoClient:
    """豆包1.6 API客户端"""
    
    def __init__(self, api_key: str = None):
        """初始化客户端
        
        Args:
            api_key: API密钥，如果为None则从文件读取
        """
        if api_key is None:
            api_key = self._load_api_key()
        
        self.api_key = api_key
        self.base_url = "https://ark.cn-beijing.volces.com/api/v3"
        self.model = "doubao-seed-1-6-250615"  # 豆包1.6模型
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        # 对话历史
        self.conversation_history: List[Dict[str, Any]] = []
        
        # 系统提示词（从提示词.md加载）
        self.system_prompt = self._load_system_prompt()
    
    def _load_api_key(self) -> str:
        """从apikey.md文件加载API密钥"""
        try:
            api_key_path = Path(__file__).parent.parent / "api接入" / "apikey.md"
            with open(api_key_path, 'r', encoding='utf-8') as f:
                return f.read().strip()
        except Exception as e:
            raise ValueError(f"无法加载API密钥: {e}")
    
    def _load_system_prompt(self) -> str:
        """从提示词.md文件加载系统提示词"""
        try:
            prompt_path = Path(__file__).parent.parent / "api接入" / "提示词.md"
            with open(prompt_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"警告：无法加载系统提示词: {e}")
            return ""
    
    def compress_image(self, image_path: str, max_size: int = 1024, quality: int = 85) -> str:
        """压缩图像并转换为base64
        
        Args:
            image_path: 图像文件路径
            max_size: 最大尺寸（像素）
            quality: JPEG质量（1-100）
            
        Returns:
            base64编码的图像字符串
        """
        try:
            with Image.open(image_path) as img:
                # 转换为RGB模式（如果需要）
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # 计算新尺寸
                width, height = img.size
                if max(width, height) > max_size:
                    if width > height:
                        new_width = max_size
                        new_height = int(height * max_size / width)
                    else:
                        new_height = max_size
                        new_width = int(width * max_size / height)
                    
                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # 保存为JPEG格式的字节流
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=quality, optimize=True)
                buffer.seek(0)
                
                # 转换为base64
                image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
                return f"data:image/jpeg;base64,{image_base64}"
                
        except Exception as e:
            raise ValueError(f"图像处理失败: {e}")
    
    def preprocess_response(self, response_text: str) -> str:
        """预处理API响应，去除<think>标签等
        
        Args:
            response_text: 原始响应文本
            
        Returns:
            处理后的响应文本
        """
        # 去除<think>...</think>标签及其内容
        cleaned_text = re.sub(r'<think>.*?</think>', '', response_text, flags=re.DOTALL)
        
        # 去除多余的空白字符
        cleaned_text = re.sub(r'\n\s*\n', '\n', cleaned_text)
        cleaned_text = cleaned_text.strip()
        
        return cleaned_text
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试API连接
        
        Returns:
            (是否成功, 消息)
        """
        try:
            test_messages = [
                {
                    "role": "user",
                    "content": "你好，请回复'连接测试成功'"
                }
            ]
            
            response = self._make_api_request(test_messages)
            
            if response and "连接测试成功" in response:
                return True, "API连接测试成功"
            else:
                return False, f"API响应异常: {response}"
                
        except Exception as e:
            return False, f"API连接测试失败: {str(e)}"
    
    def _make_api_request(self, messages: List[Dict[str, Any]]) -> str:
        """发送API请求
        
        Args:
            messages: 消息列表
            
        Returns:
            API响应内容
        """
        # 构建完整的消息列表（包含系统提示词）
        full_messages = []
        
        if self.system_prompt:
            full_messages.append({
                "role": "system",
                "content": self.system_prompt
            })
        
        full_messages.extend(messages)
        
        payload = {
            "model": self.model,
            "messages": full_messages,
            "temperature": 0.1,
            "max_tokens": 4000,
            "stream": False
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            response.raise_for_status()
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                return self.preprocess_response(content)
            else:
                raise ValueError(f"API响应格式异常: {result}")
                
        except requests.exceptions.RequestException as e:
            raise ValueError(f"API请求失败: {e}")
        except json.JSONDecodeError as e:
            raise ValueError(f"API响应解析失败: {e}")
    
    def send_message(self, text: str, image_path: str = None) -> str:
        """发送消息（支持文本和图像）
        
        Args:
            text: 文本消息
            image_path: 图像文件路径（可选）
            
        Returns:
            AI响应
        """
        # 构建消息内容
        content = []
        
        # 添加文本内容
        if text:
            content.append({
                "type": "text",
                "text": text
            })
        
        # 添加图像内容
        if image_path and os.path.exists(image_path):
            try:
                image_base64 = self.compress_image(image_path)
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": image_base64
                    }
                })
            except Exception as e:
                print(f"图像处理失败: {e}")
        
        # 构建消息
        message = {
            "role": "user",
            "content": content if len(content) > 1 else text
        }
        
        # 添加到对话历史
        self.conversation_history.append(message)
        
        # 发送请求
        try:
            response = self._make_api_request(self.conversation_history)
            
            # 添加AI响应到对话历史
            self.conversation_history.append({
                "role": "assistant",
                "content": response
            })
            
            return response
            
        except Exception as e:
            # 如果请求失败，移除刚添加的用户消息
            if self.conversation_history and self.conversation_history[-1] == message:
                self.conversation_history.pop()
            raise e
    
    def clear_conversation(self):
        """清空对话历史"""
        self.conversation_history = []
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        return self.conversation_history.copy()
    
    def extract_json_from_response(self, response: str) -> Optional[Dict[str, Any]]:
        """从响应中提取JSON对象
        
        Args:
            response: AI响应文本
            
        Returns:
            提取的JSON对象，如果没有找到则返回None
        """
        try:
            # 查找JSON代码块
            json_pattern = r'```json\s*(.*?)\s*```'
            matches = re.findall(json_pattern, response, re.DOTALL)
            
            if matches:
                # 尝试解析第一个JSON块
                json_str = matches[0].strip()
                return json.loads(json_str)
            
            # 如果没有找到代码块，尝试直接解析整个响应
            # 查找可能的JSON对象
            json_obj_pattern = r'\{.*\}'
            match = re.search(json_obj_pattern, response, re.DOTALL)
            if match:
                return json.loads(match.group())
            
            return None
            
        except json.JSONDecodeError:
            return None
        except Exception:
            return None
