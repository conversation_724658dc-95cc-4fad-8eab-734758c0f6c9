from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect, Body
import asyncio
import os
import json
from typing import List, Dict
from pydantic import BaseModel
import websockets
import traceback
import time

# 创建路由
router = APIRouter()

# 定义配置模型
class RaspberryConfig(BaseModel):
    host: str
    port: int = 8765

# 树莓派WebSocket连接配置
RASPBERRY_PI_HOST = "***************"  # 需要改为树莓派的实际IP地址
RASPBERRY_PI_PORT = 8765

# WebSocket连接管理器
class MultimodalConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        # 保存最近的消息历史，以便新连接的客户端能获取历史记录
        self.message_history = []
        self.max_history_size = 50  # 最多保存50条消息
        self.lock = asyncio.Lock()  # 添加锁以防止并发修改连接列表

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        async with self.lock:
            self.active_connections.append(websocket)
        
        # 发送历史消息给新连接的客户端
        for message in self.message_history:
            try:
                await websocket.send_json(message)
                await asyncio.sleep(0.01)  # 短暂延迟防止消息拥堵
            except Exception as e:
                print(f"发送历史消息失败: {e}")
                break
        
        # 发送连接状态信息
        try:
            global raspberry_ws
            status = "connected" if raspberry_ws is not None else "disconnected"
            await websocket.send_json({
                "type": "connection_status",
                "data": {"status": status, "timestamp": time.time()}
            })
        except Exception as e:
            print(f"发送连接状态失败: {e}")

    def disconnect(self, websocket: WebSocket):
        async def _disconnect():
            async with self.lock:
                if websocket in self.active_connections:
                    self.active_connections.remove(websocket)
            print(f"客户端已断开连接，当前连接数: {len(self.active_connections)}")
        
        # 创建任务但不等待
        asyncio.create_task(_disconnect())

    async def broadcast(self, message: dict):
        # 将消息添加到历史记录
        self.add_to_history(message)
        
        # 需要移除的断开连接
        disconnected_clients = []
        
        # 打印消息类型用于调试
        print(f"广播消息: {message.get('type')}")
        
        # 广播消息到所有连接
        async with self.lock:
            for connection in self.active_connections:
                try:
                    await connection.send_json(message)
                except WebSocketDisconnect:
                    disconnected_clients.append(connection)
                    print("广播时检测到客户端断开")
                except Exception as e:
                    disconnected_clients.append(connection)
                    print(f"广播消息失败: {e}")
            
            # 移除断开的客户端
            for client in disconnected_clients:
                if client in self.active_connections:
                    self.active_connections.remove(client)
            
            # 打印剩余连接数
            if disconnected_clients:
                print(f"移除了 {len(disconnected_clients)} 个断开的客户端，剩余 {len(self.active_connections)} 个连接")
            
    def add_to_history(self, message: dict):
        """将消息添加到历史记录，保持历史记录大小"""
        # 只保存特定类型的消息
        if message.get("type") in ["text", "image", "connection_status"]:
            self.message_history.append(message)
            # 如果历史记录超过最大大小，删除最早的消息
            if len(self.message_history) > self.max_history_size:
                self.message_history = self.message_history[-self.max_history_size:]

manager = MultimodalConnectionManager()

# 与树莓派的WebSocket连接
raspberry_ws = None
raspberry_ws_lock = asyncio.Lock()
raspberry_ws_task = None
heartbeat_interval = 30  # 心跳间隔（秒）

async def connect_to_raspberry_pi():
    """连接到树莓派WebSocket服务器"""
    global raspberry_ws
    try:
        uri = f"ws://{RASPBERRY_PI_HOST}:{RASPBERRY_PI_PORT}"
        print(f"正在连接到树莓派: {uri}")
        # 设置ping_interval和ping_timeout参数来启用自动心跳
        raspberry_ws = await websockets.connect(
            uri, 
            ping_interval=heartbeat_interval,
            ping_timeout=10,
            close_timeout=5
        )
        print(f"已连接到树莓派: {uri}")
        
        # 发送初始连接消息
        await raspberry_ws.send(json.dumps({
            "type": "connection_init",
            "data": {
                "client": "backend",
                "timestamp": time.time()
            }
        }))
        
        # 广播连接状态给所有前端客户端
        await manager.broadcast({
            "type": "connection_status",
            "data": {"status": "connected", "timestamp": time.time()}
        })
        
        return True
    except Exception as e:
        print(f"连接树莓派失败: {e}")
        traceback.print_exc()
        
        # 广播连接失败状态
        await manager.broadcast({
            "type": "connection_status",
            "data": {"status": "disconnected", "error": str(e), "timestamp": time.time()}
        })
        
        return False

# 心跳保持函数
async def keep_connection_alive():
    """定期发送心跳消息保持连接活跃"""
    global raspberry_ws
    try:
        while raspberry_ws:
            try:
                # 发送心跳消息
                await raspberry_ws.send(json.dumps({
                    "type": "heartbeat",
                    "data": {"timestamp": time.time()}
                }))
                print("已发送心跳消息")
                await asyncio.sleep(heartbeat_interval)
            except websockets.exceptions.ConnectionClosed:
                print("心跳时发现连接已关闭")
                break
            except Exception as e:
                print(f"心跳错误: {e}")
                await asyncio.sleep(5)  # 出错后短暂等待再重试
    except Exception as e:
        print(f"保持连接活跃失败: {e}")

# 消息中继函数 - 将消息从树莓派转发到前端
async def raspberry_message_relay():
    global raspberry_ws
    if raspberry_ws is None:
        return
    
    try:
        # 启动心跳任务
        heartbeat_task = asyncio.create_task(keep_connection_alive())
        
        # 通知前端连接已建立
        await manager.broadcast({
            "type": "connection_status",
            "data": {"status": "connected", "timestamp": time.time()}
        })
        
        # 持续接收树莓派消息并转发
        while True:
            try:
                message = await raspberry_ws.recv()
                data = json.loads(message)
                
                # 处理不同类型的消息
                message_type = data.get("type", "")
                
                # 忽略心跳响应消息的记录
                if message_type == "heartbeat":
                    print("收到心跳响应")
                    continue
                
                # 转发到所有前端连接
                await manager.broadcast(data)
                
                # 记录消息类型和简短内容
                if message_type == "text":
                    text = data.get("data", {}).get("text", "")
                    role = data.get("data", {}).get("role", "unknown")
                    print(f"收到{role}文本消息: {text[:50]}...")
                elif message_type == "image":
                    print(f"收到图像消息")
                else:
                    print(f"收到其他类型消息: {message_type}")
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}, 收到的消息: {message[:100]}")
            except websockets.exceptions.ConnectionClosed as e:
                print(f"与树莓派的连接已关闭: {e}")
                await manager.broadcast({
                    "type": "connection_status",
                    "data": {"status": "disconnected", "error": "连接已关闭", "timestamp": time.time()}
                })
                break
            except Exception as e:
                print(f"处理消息错误: {e}")
                await asyncio.sleep(1)  # 添加短暂延迟防止错误循环过快消耗资源
                
    except websockets.exceptions.ConnectionClosed as e:
        print(f"与树莓派的连接已关闭: {e}")
        raspberry_ws = None
        await manager.broadcast({
            "type": "connection_status",
            "data": {"status": "disconnected", "error": str(e), "timestamp": time.time()}
        })
    except Exception as e:
        print(f"消息中继错误: {e}")
        traceback.print_exc()
        raspberry_ws = None
        await manager.broadcast({
            "type": "connection_status",
            "data": {"status": "disconnected", "error": str(e), "timestamp": time.time()}
        })
    finally:
        # 取消心跳任务
        if 'heartbeat_task' in locals() and heartbeat_task:
            heartbeat_task.cancel()

# WebSocket路由端点
@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        # 确保与树莓派的连接
        global raspberry_ws, raspberry_ws_task
        
        # 发送当前连接状态
        try:
            status = "connected" if raspberry_ws is not None else "disconnected"
            await websocket.send_json({
                "type": "connection_status",
                "data": {"status": status, "timestamp": time.time()}
            })
        except Exception as e:
            print(f"发送初始状态失败: {e}")
        
        async with raspberry_ws_lock:
            if raspberry_ws is None:
                if not await connect_to_raspberry_pi():
                    await websocket.send_json({
                        "type": "connection_error",
                        "data": {"message": "无法连接到树莓派"}
                    })
                    # 继续执行，但已通知前端连接失败
                # 启动消息中继任务，确保只有一个任务在运行
                if raspberry_ws_task is None or raspberry_ws_task.done():
                    raspberry_ws_task = asyncio.create_task(raspberry_message_relay())
            else:
                # 如果已连接，再次广播连接状态
                await websocket.send_json({
                    "type": "connection_status",
                    "data": {"status": "connected", "timestamp": time.time()}
                })
        
        # 处理来自前端的消息
        while True:
            data = await websocket.receive_json()
            print(f"从前端收到消息: {data.get('type', 'unknown')}")
            
            async with raspberry_ws_lock:
                if raspberry_ws:
                    try:
                        # 直接转发前端消息到树莓派
                        await raspberry_ws.send(json.dumps(data))
                        print(f"消息已转发到树莓派")
                    except websockets.exceptions.ConnectionClosed:
                        print("转发消息时发现连接已关闭")
                        # 树莓派连接丢失，尝试重连
                        if await connect_to_raspberry_pi():
                            if raspberry_ws_task is None or raspberry_ws_task.done():
                                raspberry_ws_task = asyncio.create_task(raspberry_message_relay())
                            await raspberry_ws.send(json.dumps(data))
                        else:
                            await websocket.send_json({
                                "type": "connection_error",
                                "data": {"message": "与树莓派的连接已断开"}
                            })
                else:
                    print("未连接到树莓派，尝试连接")
                    # 树莓派连接丢失，尝试重连
                    if await connect_to_raspberry_pi():
                        if raspberry_ws_task is None or raspberry_ws_task.done():
                            raspberry_ws_task = asyncio.create_task(raspberry_message_relay())
                        await raspberry_ws.send(json.dumps(data))
                    else:
                        await websocket.send_json({
                            "type": "connection_error",
                            "data": {"message": "与树莓派的连接已断开"}
                        })
    
    except WebSocketDisconnect:
        print(f"前端WebSocket断开连接")
        manager.disconnect(websocket)
    except Exception as e:
        print(f"WebSocket错误: {str(e)}")
        traceback.print_exc()
        manager.disconnect(websocket)

# 更新树莓派连接配置
@router.post("/config")
async def update_raspberry_config(config: RaspberryConfig):
    global RASPBERRY_PI_HOST, RASPBERRY_PI_PORT, raspberry_ws, raspberry_ws_task
    
    print(f"更新树莓派配置: 主机={config.host}, 端口={config.port}")
    
    # 如果已经连接，先断开
    if raspberry_ws:
        try:
            await raspberry_ws.close()
        except:
            pass
        raspberry_ws = None
    
    # 取消任务
    if raspberry_ws_task:
        raspberry_ws_task.cancel()
        raspberry_ws_task = None
    
    # 更新配置
    RASPBERRY_PI_HOST = config.host
    RASPBERRY_PI_PORT = config.port
    
    return {"status": "success", "message": "配置已更新"}

# 初始化与树莓派的连接
@router.post("/connect")
async def init_raspberry_connection():
    global raspberry_ws, raspberry_ws_task
    
    print(f"尝试连接到树莓派: {RASPBERRY_PI_HOST}:{RASPBERRY_PI_PORT}")
    
    async with raspberry_ws_lock:
        if raspberry_ws is not None:
            print("已有活跃连接到树莓派")
            return {"status": "success", "message": "已连接到树莓派"}
        
        try:
            if await connect_to_raspberry_pi():
                # 启动消息中继任务
                if raspberry_ws_task is None or raspberry_ws_task.done():
                    raspberry_ws_task = asyncio.create_task(raspberry_message_relay())
                print(f"成功连接到树莓派")
                return {"status": "success", "message": "已连接到树莓派"}
            else:
                print(f"无法连接到树莓派: {RASPBERRY_PI_HOST}:{RASPBERRY_PI_PORT}")
                raise HTTPException(status_code=500, detail=f"无法连接到树莓派: {RASPBERRY_PI_HOST}:{RASPBERRY_PI_PORT}")
        except Exception as e:
            print(f"连接树莓派时发生错误: {str(e)}")
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"连接树莓派失败: {str(e)}")

# 获取消息历史
@router.get("/history")
async def get_message_history():
    """获取最近的消息历史"""
    return {"messages": manager.message_history}

# 获取连接状态
@router.get("/status")
async def get_connection_status():
    """获取树莓派连接状态"""
    global raspberry_ws
    
    is_connected = raspberry_ws is not None
    
    try:
        # 如果有连接，尝试发送一个ping来确认连接有效
        if is_connected:
            # 发送一个ping测试连接
            pong_waiter = await raspberry_ws.ping()
            await asyncio.wait_for(pong_waiter, timeout=2.0)
    except:
        is_connected = False
        raspberry_ws = None
    
    return {
        "status": "connected" if is_connected else "disconnected",
        "host": RASPBERRY_PI_HOST,
        "port": RASPBERRY_PI_PORT
    }

# 测试拍照功能
@router.post("/test_photo")
async def test_photo_capture():
    """测试拍照功能"""
    global raspberry_ws
    
    if raspberry_ws is None:
        # 尝试先连接
        try:
            await init_raspberry_connection()
        except HTTPException:
            return {"status": "error", "message": "无法连接到树莓派"}
    
    try:
        # 发送拍照命令
        await raspberry_ws.send(json.dumps({
            "type": "command",
            "data": {"command": "take_photo", "timestamp": time.time()}
        }))
        
        print("已发送拍照命令")
        return {"status": "success", "message": "已发送拍照命令，请检查前端是否收到图像"}
    except Exception as e:
        print(f"发送拍照命令失败: {e}")
        traceback.print_exc()
        return {"status": "error", "message": f"发送拍照命令失败: {str(e)}"}

# 测试发送文本
@router.post("/test_message")
async def test_send_message(message: dict = Body(...)):
    """测试发送文本消息"""
    global raspberry_ws
    
    text = message.get("message", "测试消息")
    
    try:
        # 广播测试消息到前端
        await manager.broadcast({
            "type": "text",
            "data": {
                "text": text,
                "role": "system",
                "timestamp": time.time()
            }
        })
        
        print(f"已发送测试消息: {text}")
        return {"status": "success", "message": "已广播测试消息到前端"}
    except Exception as e:
        print(f"发送测试消息失败: {e}")
        traceback.print_exc()
        return {"status": "error", "message": f"发送测试消息失败: {str(e)}"}