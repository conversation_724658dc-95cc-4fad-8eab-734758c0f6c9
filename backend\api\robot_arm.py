#!/usr/bin/env python
# Robot arm control API module for the platform

import json
import time
import threading
import zmq
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

# Define models for API endpoints
class ConnectRequest(BaseModel):
    ip: str = "************"
    port: int = 30080

class KeyboardCommand(BaseModel):
    command: str

# Create router
router = APIRouter()

# Global robot arm client instance
robot_client = None
client_lock = threading.Lock()

# 保存主臂控制的当前状态
main_arm_status = {
    "connected": False,           # 是否已连接到服务器
    "script_running": False,      # 脚本是否正在运行
    "script_pid": None,           # 运行中脚本的PID
    "ssh_client": None            # SSH连接客户端
}

class RobotArmClient:
    def __init__(self, ip="************", port=30080):
        self.ip = ip
        self.port = port
        self.running = False

        # Initialize ZMQ
        self.context = zmq.Context()
        self.cmd_socket = self.context.socket(zmq.PUSH)
        self.cmd_socket.setsockopt(zmq.CONFLATE, 1)  # Only keep latest message

        # 设置较短的连接超时时间
        self.cmd_socket.setsockopt(zmq.RCVTIMEO, 1000)  # 1秒超时
        self.cmd_socket.setsockopt(zmq.SNDTIMEO, 1000)  # 1秒超时

        # 连接到服务器
        self.cmd_socket.connect(f"tcp://{self.ip}:{self.port}")

        # Response socket
        self.resp_socket = self.context.socket(zmq.PULL)
        self.resp_socket.setsockopt(zmq.CONFLATE, 1)
        self.resp_socket.setsockopt(zmq.RCVTIMEO, 1000)  # 1秒超时
        self.resp_socket.connect(f"tcp://{self.ip}:{self.port + 1}")

        # Thread-safe lock for key press management
        self.lock = threading.Lock()

        # Define key mappings
        self.key_to_joint_increase = {
            'w': 0,  # Move forward
            'a': 1,  # Move right
            'r': 2,  # Move up
            'q': 3,  # Roll +
            'g': 4,  # Pitch +
            'z': 5,  # Gripper +
        }

        self.key_to_joint_decrease = {
            's': 0,  # Move backward
            'd': 1,  # Move left
            'f': 2,  # Move down
            'e': 3,  # Roll -
            't': 4,  # Pitch -
            'c': 5,  # Gripper -
        }

        # Dictionary to track currently pressed keys and their direction
        self.keys_pressed = {}

        # Control command sending frequency
        self.cmd_interval = 0.01  # 10ms, 100Hz
        self.last_cmd_time = {}

        # Create response handler thread
        self.response_thread = threading.Thread(target=self.response_handler, daemon=True)

        # Create processing thread
        self.processing_thread = None

    def handle_key_command(self, command):
        if not self.running:
            return False, "键盘控制未启动"

        if command.endswith("_stop"):
            # Key release command
            k = command.split("_")[0]
            if k in self.keys_pressed:
                with self.lock:
                    del self.keys_pressed[k]
                    if k in self.last_cmd_time:
                        del self.last_cmd_time[k]
            return True, f"停止按键 {k}"
        else:
            # Key press command
            k = command
            if k in self.key_to_joint_increase:
                with self.lock:
                    if k not in self.keys_pressed:
                        self.keys_pressed[k] = 1  # Increase direction
                return True, f"按下按键 {k} (增加)"
            elif k in self.key_to_joint_decrease:
                with self.lock:
                    if k not in self.keys_pressed:
                        self.keys_pressed[k] = -1  # Decrease direction
                return True, f"按下按键 {k} (减少)"
            elif k == "0":
                # Reset command
                self.send_reset_command()
                return True, "重置机械臂位置"
            elif k == "p":
                # Ping command to test connection
                self.send_ping_command()
                return True, "发送连接测试"
            return False, f"未知命令: {command}"

    def response_handler(self):
        while self.running:
            try:
                response = self.resp_socket.recv_json(zmq.NOBLOCK)
                if response:
                    status = response.get("status", "unknown")
                    message = response.get("message", "")
            except zmq.Again:
                pass
            except Exception as e:
                print(f"Response handling error: {e}")

            time.sleep(0.01)

    def send_position_command(self, position_idx, direction):
        cmd = {
            "command": "move",
            "position_idx": position_idx,
            "direction": direction,
            "type": "position",
            "increment": 0.0020  # POSITION_INCREMENT
        }
        self.cmd_socket.send_json(cmd)

    def send_joint_command(self, joint_idx, direction):
        cmd = {
            "command": "move",
            "position_idx": joint_idx,
            "direction": direction,
            "type": "joint",
            "increment": 0.020  # JOINT_INCREMENT
        }
        self.cmd_socket.send_json(cmd)

    def send_reset_command(self):
        cmd = {"command": "init"}
        self.cmd_socket.send_json(cmd)

    def send_ping_command(self):
        cmd = {"command": "ping"}
        self.cmd_socket.send_json(cmd)

    def send_server_disconnect_command(self):
        print(f"[ROBOT_ARM_API_LOG] RobotArmClient.send_server_disconnect_command() called.")
        cmd = {"command": "disconnect"}
        try:
            if self.cmd_socket and not self.cmd_socket.closed:
                print(f"[ROBOT_ARM_API_LOG] Attempting to send disconnect command to LRS via cmd_socket: {cmd}")
                self.cmd_socket.send_json(cmd)
                print(f"[ROBOT_ARM_API_LOG] Successfully sent disconnect command to LRS.")
            else:
                print(f"[ROBOT_ARM_API_LOG] cmd_socket closed or not available, cannot send disconnect command to LRS.")
        except Exception as e:
            print(f"[ROBOT_ARM_API_LOG] Error sending server_disconnect command via ZMQ: {e}")

    def process_keys(self):
        current_time = time.time()

        with self.lock:
            for k, direction in self.keys_pressed.items():
                # Check if sending interval has passed
                if k not in self.last_cmd_time or current_time - self.last_cmd_time[k] >= self.cmd_interval:
                    if k in self.key_to_joint_increase:
                        position_idx = self.key_to_joint_increase[k]
                        if position_idx == 1 or position_idx == 5:
                            # Direct joint controls
                            joint_idx = 0 if position_idx == 1 else 5
                            self.send_joint_command(joint_idx, direction)
                        else:
                            # Position controls
                            self.send_position_command(position_idx, direction)

                    elif k in self.key_to_joint_decrease:
                        position_idx = self.key_to_joint_decrease[k]
                        if position_idx == 1 or position_idx == 5:
                            # Direct joint controls
                            joint_idx = 0 if position_idx == 1 else 5
                            self.send_joint_command(joint_idx, direction)
                        else:
                            # Position controls
                            self.send_position_command(position_idx, direction)

                    # Update last send time for this key
                    self.last_cmd_time[k] = current_time

    def _processing_loop(self):
        """内部处理循环，在单独的线程中运行"""
        while self.running:
            self.process_keys()
            time.sleep(0.01)  # 10ms, 100Hz update rate

    def start_keyboard_control(self):
        """启动键盘控制接口"""
        if self.running:
            return False

        self.running = True

        # 发送连接测试
        self.send_ping_command()

        # 启动响应处理线程
        self.response_thread.start()

        # 启动处理线程
        self.processing_thread = threading.Thread(target=self._processing_loop, daemon=True)
        self.processing_thread.start()

        return True

    def stop_keyboard_control(self):
        """停止键盘控制接口"""
        if not self.running:
            return False

        self.cleanup()
        return True

    def cleanup(self):
        """清理资源"""
        self.running = False

        # ZMQ 清理
        if hasattr(self, 'cmd_socket'):
            self.cmd_socket.close()
        if hasattr(self, 'resp_socket'):
            self.resp_socket.close()
        if hasattr(self, 'context'):
            self.context.term()

    def test_connection(self):
        """测试连接是否有效"""
        print(f"[ROBOT_ARM_API_LOG] Attempting RobotArmClient.test_connection() to LRS at {self.ip}:{self.port}")
        try:
            print(f"[ROBOT_ARM_API_LOG] Sending ping command to LRS.")
            self.send_ping_command()

            start_time = time.time()
            timeout = 2.0  # 2秒超时
            response_received = None

            while time.time() - start_time < timeout:
                try:
                    # print(f"[ROBOT_ARM_API_LOG] Attempting to receive from resp_socket...") # Can be very verbose
                    response = self.resp_socket.recv_json(zmq.NOBLOCK)
                    if response:
                        response_received = response # Store last received response
                        print(f"[ROBOT_ARM_API_LOG] Received response from LRS: {response}")
                        status = response.get("status", "unknown")
                        message = response.get("message", "")

                        if status == "ok" and message == "pong (ZMQ OK, robot arm connected)":
                            print("[ROBOT_ARM_API_LOG] test_connection successful: Correct pong received.")
                            return True
                        else:
                            print(f"[ROBOT_ARM_API_LOG] test_connection: Received pong, but message or status incorrect. Status: {status}, Message: '{message}'")
                            # Continue trying until timeout in case a previous (e.g. conflated) message was received

                except zmq.Again:
                    # No message available, sleep briefly and continue polling
                    time.sleep(0.05) # Reduced sleep from 0.1 to 0.05 for slightly faster polling
                    continue
                except json.JSONDecodeError as jde:
                    print(f"[ROBOT_ARM_API_LOG] JSONDecodeError in test_connection from LRS resp_socket: {jde}. Raw response likely not JSON.")
                    # Potentially received a non-JSON message, which is an error condition for this check.
                    return False # Treat as failure
                except Exception as e:
                    print(f"[ROBOT_ARM_API_LOG] Exception while receiving from LRS resp_socket: {e}")
                    return False # Treat as failure

            # Timeout occurred
            if response_received:
                print(f"[ROBOT_ARM_API_LOG] test_connection timed out. Last LRS response received was: {response_received}")
            else:
                print(f"[ROBOT_ARM_API_LOG] test_connection timed out waiting for pong from LRS. No response received.")
            return False
        except Exception as e:
            print(f"[ROBOT_ARM_API_LOG] test_connection failed (exception during send/initial setup): {e}")
            return False


# API Endpoints
@router.post("/connect")
async def connect_to_robot(request: ConnectRequest):
    """
    连接到机械臂
    """
    global robot_client

    with client_lock:
        # 如果已有连接，先断开
        if robot_client is not None:
            try:
                robot_client.stop_keyboard_control()
                robot_client.cleanup()
            except Exception as e:
                pass
            robot_client = None

        try:
            # 创建新连接
            robot_client = RobotArmClient(ip=request.ip, port=request.port)

            # 测试连接是否有效
            if robot_client.test_connection():
                return {
                    "status": "success",
                    "message": f"已连接到机械臂 {request.ip}:{request.port}"
                }
            else:
                # 连接失败，清理资源
                robot_client.cleanup()
                robot_client = None
                return {
                    "status": "error",
                    "message": f"无法连接到机械臂服务 {request.ip}:{request.port}，请确认机械臂服务已启动"
                }
        except Exception as e:
            # 连接出错
            if robot_client:
                try:
                    robot_client.cleanup()
                except:
                    pass
                robot_client = None
            raise HTTPException(status_code=500, detail=f"连接失败: {str(e)}")

@router.post("/start")
async def start_robot_control():
    """
    启动机械臂键盘控制
    """
    global robot_client

    with client_lock:
        if robot_client is None:
            raise HTTPException(status_code=400, detail="未连接到机械臂")

        try:
            success = robot_client.start_keyboard_control()
            return {
                "status": "success" if success else "error",
                "message": "键盘控制已启动" if success else "启动键盘控制失败，可能已经在运行"
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"启动控制失败: {str(e)}")

@router.post("/stop")
async def stop_robot_control():
    """
    停止机械臂键盘控制
    """
    global robot_client

    with client_lock:
        if robot_client is None:
            raise HTTPException(status_code=400, detail="未连接到机械臂")

        try:
            success = robot_client.stop_keyboard_control()
            return {
                "status": "success" if success else "error",
                "message": "键盘控制已停止" if success else "停止键盘控制失败，可能未运行"
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"停止控制失败: {str(e)}")

@router.post("/disconnect")
async def disconnect_robot():
    """
    断开与机械臂的连接并清理资源
    """
    print(f"[ROBOT_ARM_API_LOG] /disconnect endpoint hit.")
    global robot_client

    with client_lock:
        if robot_client is None:
            print(f"[ROBOT_ARM_API_LOG] /disconnect: No active robot_client to disconnect.")
            return {
                "status": "success",
                "message": "未连接到机械臂，无需断开"
            }

        try:
            print(f"[ROBOT_ARM_API_LOG] /disconnect: Active robot_client found. Proceeding with disconnect.")
            # 1. Explicitly tell the robot server to disconnect its hardware
            print(f"[ROBOT_ARM_API_LOG] /disconnect: Calling robot_client.send_server_disconnect_command().")
            robot_client.send_server_disconnect_command()

            # Give a very brief moment for the ZMQ message to be sent and potentially processed
            print(f"[ROBOT_ARM_API_LOG] /disconnect: Pausing briefly after sending disconnect command.")
            time.sleep(0.2) # Increased slightly to 0.2s for more reliable ZMQ send before client cleanup

            # 2. Stop keyboard control loop in this backend client (if running)
            if robot_client.running:
                print(f"[ROBOT_ARM_API_LOG] /disconnect: Robot_client is running, calling stop_keyboard_control().")
                robot_client.stop_keyboard_control() # This stops the key-pressing loop in RobotArmClient
            else:
                print(f"[ROBOT_ARM_API_LOG] /disconnect: Robot_client was not running, skipping stop_keyboard_control().")

            # 3. Cleanup ZMQ resources for this backend client
            print(f"[ROBOT_ARM_API_LOG] /disconnect: Calling robot_client.cleanup().")
            robot_client.cleanup() # This closes RobotArmClient's ZMQ sockets
            print(f"[ROBOT_ARM_API_LOG] /disconnect: robot_client.cleanup() completed.")

            robot_client = None # Clear the global client instance
            print(f"[ROBOT_ARM_API_LOG] /disconnect: robot_client set to None. Disconnect process complete.")
            return {
                "status": "success",
                "message": "已成功断开与机械臂的连接"
            }
        except Exception as e:
            print(f"[ROBOT_ARM_API_LOG] /disconnect: Exception during disconnect process: {e}")
            # Attempt to ensure client is cleared even on error
            try:
                if robot_client:
                    robot_client.cleanup() # Try to cleanup again if possible
            except Exception as cleanup_e:
                print(f"[ROBOT_ARM_API_LOG] /disconnect: Exception during secondary cleanup attempt: {cleanup_e}")
            finally:
                robot_client = None
            raise HTTPException(status_code=500, detail=f"断开连接失败: {str(e)}")

@router.post("/keyboard")
async def handle_keyboard_command(command: KeyboardCommand):
    """
    处理键盘控制命令

    命令格式示例: 'w', 'a', 's', 'd', 'w_stop', 'a_stop', 's_stop', 'd_stop'
    """
    global robot_client

    with client_lock:
        if robot_client is None:
            raise HTTPException(status_code=400, detail="未连接到机械臂")

        try:
            success, message = robot_client.handle_key_command(command.command)
            return {
                "status": "success" if success else "error",
                "command": command.command,
                "message": message
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"命令执行失败: {str(e)}")
