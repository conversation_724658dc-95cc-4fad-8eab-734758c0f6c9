from fastapi import APIRouter, HTTPException, WebSocket, Body
from pydantic import BaseModel
from typing import Dict, Any, Optional
import asyncio
import time


# 控制命令模型
class KeyboardCommand(BaseModel):
    command: str


class RecordStatus(BaseModel):
    status: str
    timestamp: float


# 创建路由
router = APIRouter()

# 全局状态
recording = False
record_start_time = 0
current_status = "待机中"


# 键盘控制
@router.post("/keyboard")
async def handle_keyboard_command(command: KeyboardCommand):
    """
    处理键盘控制命令

    命令格式: 'w', 'a', 's', 'd', 'w_stop', 'a_stop', 's_stop', 'd_stop'
    """
    global current_status

    try:
        # 在实际应用中，这里需要实现与硬件通信的逻辑
        # 目前仅返回接收到的命令确认
        current_status = f"执行命令: {command.command}"

        # 模拟控制过程
        await asyncio.sleep(0.1)

        return {
            "status": "success",
            "command": command.command,
            "message": f"执行控制命令: {command.command}"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"控制命令执行失败: {str(e)}")


# 按钮控制
@router.post("/button")
async def handle_button_command(command: Dict[str, Any] = Body(...)):
    """
    处理按钮控制命令
    """
    global current_status

    try:
        # 提取方向命令
        direction = command.get("direction")
        action = command.get("action", "press")  # press 或 release

        # 在实际应用中，这里需要实现与硬件通信的逻辑
        current_status = f"执行方向命令: {direction} ({action})"

        # 模拟控制过程
        await asyncio.sleep(0.1)

        return {
            "status": "success",
            "direction": direction,
            "action": action,
            "message": f"执行方向命令: {direction} ({action})"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"方向命令执行失败: {str(e)}")


# 开始录制
@router.post("/record/start")
async def start_recording():
    """
    开始录制操作过程
    """
    global recording, record_start_time, current_status

    try:
        if recording:
            return {
                "status": "warning",
                "message": "录制已经在进行中"
            }

        # 开始录制
        recording = True
        record_start_time = time.time()
        current_status = "录制中..."

        return {
            "status": "success",
            "message": "开始录制",
            "timestamp": record_start_time
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"开始录制失败: {str(e)}")


# 停止录制
@router.post("/record/stop")
async def stop_recording():
    """
    停止录制操作过程
    """
    global recording, record_start_time, current_status

    try:
        if not recording:
            return {
                "status": "warning",
                "message": "当前没有进行录制"
            }

        # 停止录制
        recording = False
        duration = time.time() - record_start_time
        current_status = "录制已停止"

        return {
            "status": "success",
            "message": "停止录制",
            "duration": duration
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"停止录制失败: {str(e)}")


# 获取状态
@router.get("/status")
async def get_status():
    """
    获取当前控制状态
    """
    global recording, record_start_time, current_status

    status_data = {
        "current_status": current_status,
        "recording": recording
    }

    if recording:
        status_data["record_duration"] = time.time() - record_start_time

    return status_data
