import re

from fastapi import APIRouter, HTTPException, Body, BackgroundTasks, Header, Request
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import cv2
import base64
import json
import time
import os
import asyncio
from openai import OpenAI
import httpx # 用于与远程机械臂服务通信

# 创建路由
router = APIRouter()

# 配置远程机械臂服务地址
# 从环境变量中获取，如果通过前端设置，config模块会动态更新这个环境变量
ARM_CONTROL_SERVICE_BASE_URL = os.getenv("ARM_SERVICE_URL", "http://192.168.174.252:8001/api/arm")
# LEROBOT_AVAILABLE 现在表示远程服务是否配置或可访问的意愿，而不是本地库
LEROBOT_AVAILABLE = True # 假设我们总是希望尝试使用远程服务

# HTTP 客户端会话
http_client = httpx.AsyncClient(timeout=30.0) # 增加超时时间

class RobotConnection(BaseModel):
    port: str = "/dev/ttyACM0" # 此端口将发送到远程服务，连接从臂（固定为从臂串口）
    # robot_type is no longer needed here as the remote service is so101 specific
    # robot_type: str = "so101"

class CameraCapture(BaseModel):
    camera_id: int = 2

class ChessMove(BaseModel):
    position: int
    model: Optional[str] = None  # 添加可选模型字段

class AnalyzeRequest(BaseModel):
    image: str
    model: Optional[str] = None  # 添加可选模型字段
    inference_model: Optional[str] = None  # 添加可选推理模型字段

# 全局变量
# robot = None # 本地不再直接管理 robot 实例
robot_connected_remote = False # 新增标志表示远程机械臂连接状态

client = OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key="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",
)

# ========== 拍照并转为 base64 ==========
def capture_and_encode_base64(camera_id=2) -> str:
    # 摄像头对象
    cap = None
    
    try:
        # 尝试多次初始化摄像头
        for attempt in range(3):
            try:
                cap = cv2.VideoCapture(camera_id, cv2.CAP_DSHOW)  # 使用DirectShow后端
                if cap.isOpened():
                    break
                time.sleep(1)  # 等待一秒再次尝试
            except Exception as e:
                print(f"[WARN] 第{attempt+1}次尝试初始化摄像头失败: {e}")
        
        if not cap or not cap.isOpened():
            raise HTTPException(status_code=500, detail="无法打开摄像头，请检查摄像头连接和ID设置")
        
        # 调整摄像头参数
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        
        # 丢弃前几帧以等待摄像头稳定
        for _ in range(5):
            cap.read()
            
        # 多次尝试读取图像
        for attempt in range(3):
            ret, frame = cap.read()
            if ret and frame is not None and frame.size > 0:
                break
            time.sleep(0.5)
            
        if not ret or frame is None or frame.size == 0:
            raise HTTPException(status_code=500, detail="读取图像失败，请尝试重新连接摄像头")
            
        _, buffer = cv2.imencode('.jpg', frame)
        b64_bytes = base64.b64encode(buffer).decode("utf-8")
        return b64_bytes
    except Exception as e:
        print(f"[ERROR] 拍照过程中出错: {e}")
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"拍照失败: {str(e)}")
    finally:
        # 确保摄像头被释放
        if cap is not None:
            cap.release()

# 用于视频流的全局摄像头对象
video_capture = None
last_frame_time = 0

# 初始化视频流
def init_video_stream(camera_id=2):
    global video_capture
    try:
        # 释放之前的摄像头对象（如果存在）
        if video_capture is not None:
            try:
                video_capture.release()
                print("[INFO] 释放之前的摄像头资源")
            except Exception as e:
                print(f"[WARN] 释放摄像头资源时出错: {e}")
            
            video_capture = None
            time.sleep(0.8)  # 延长等待时间，确保资源完全释放
        
        # 尝试不同的摄像头后端和参数
        backends = [cv2.CAP_DSHOW, cv2.CAP_ANY]
        
        success = False
        # 尝试多个后端，直到找到一个可用的
        for backend in backends:
            try:
                print(f"[INFO] 尝试使用后端 {backend} 初始化摄像头 {camera_id}")
                video_capture = cv2.VideoCapture(camera_id, backend)
                
                if video_capture.isOpened():
                    print(f"[INFO] 使用后端 {backend} 成功打开摄像头 {camera_id}")
                    
                    # 设置分辨率和参数
                    video_capture.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                    video_capture.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                    video_capture.set(cv2.CAP_PROP_FPS, 15)          # 降低帧率以提高稳定性
                    video_capture.set(cv2.CAP_PROP_BUFFERSIZE, 1)    # 减小缓冲区大小
                    
                    # 丢弃前几帧，确保摄像头稳定
                    for _ in range(5):
                        ret, frame = video_capture.read()
                        if not ret:
                            print("[WARN] 摄像头预热时读取帧失败")
                        time.sleep(0.1)
                    
                    print(f"[INFO] 成功初始化视频流，摄像头 ID: {camera_id}")
                    success = True
                    break  # 成功初始化后退出循环
                else:
                    print(f"[INFO] 后端 {backend} 打开摄像头 {camera_id} 失败")
                    if video_capture is not None:
                        video_capture.release()
                        video_capture = None
            except Exception as e:
                print(f"[ERROR] 使用后端 {backend} 初始化摄像头时出错: {e}")
                if video_capture is not None:
                    try:
                        video_capture.release()
                    except:
                        pass
                    video_capture = None
        
        if not success:
            print(f"[ERROR] 尝试所有后端后仍无法打开摄像头 ID: {camera_id}")
            return False
            
        return True
    except Exception as e:
        print(f"[ERROR] 初始化视频流时出错: {e}")
        return False

# 获取视频帧
def get_video_frame():
    global video_capture, last_frame_time
    
    if video_capture is None:
        print("[WARN] 视频流未初始化")
        return None
        
    if not video_capture.isOpened():
        print("[WARN] 视频流已断开连接")
        return None
    
    # 限制帧率，但不要过低，避免前端等待时间过长
    current_time = time.time()
    if current_time - last_frame_time < 0.066:  # 约15fps
        return None
    
    try:
        # 清空缓冲区，读取最新的帧
        video_capture.grab()  # 减少grab次数，避免资源占用过高
        
        # 尝试多次读取帧，直到成功或达到最大尝试次数
        for attempt in range(3):
            ret, frame = video_capture.read()
            
            if ret and frame is not None and frame.size > 0:
                # 检查帧是否全黑或过暗
                brightness = cv2.mean(frame)[0]
                if brightness < 5:  # 平均亮度很低
                    print(f"[WARN] 第{attempt+1}次获取的帧过暗(亮度:{brightness:.1f})，重试...")
                    time.sleep(0.05)
                    continue
                
                # 应用图像处理提高质量
                # 提高对比度
                alpha = 1.2  # 对比度增强因子
                beta = 15    # 亮度增强因子
                frame = cv2.convertScaleAbs(frame, alpha=alpha, beta=beta)
                
                last_frame_time = current_time
                
                try:
                    # 转换为JPEG，降低质量以减小数据量提高传输稳定性
                    _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 70])
                    return base64.b64encode(buffer).decode('utf-8')
                except Exception as encode_error:
                    print(f"[ERROR] 编码视频帧失败: {encode_error}")
                    return None
            
            if attempt < 2:  # 如果不是最后一次尝试
                print(f"[WARN] 第{attempt+1}次获取视频帧失败，重试...")
                time.sleep(0.05)
        
        print("[ERROR] 多次尝试后仍无法获取有效视频帧")
        return None
    except Exception as e:
        print(f"[ERROR] 获取视频帧时出错: {e}")
        return None

# 关闭视频流
def close_video_stream():
    global video_capture
    
    if video_capture is None:
        return
        
    try:
        video_capture.release()
        print("[INFO] 视频流已关闭")
    except Exception as e:
        print(f"[ERROR] 关闭视频流时出错: {e}")
    finally:
        video_capture = None

# ========== 多模态识别并加重试机制 ==========
async def analyze_board_from_base64(base64_str: str, model_name: str = "gemini-2.5-pro", max_retries: int = 3):
    # 根据需求修改提示词，只要求输出棋子位置
    prompt = """
请根据下方图像，识别井字棋棋盘的当前状态（棋盘已经用黑色笔画在白纸上）。

- 棋盘为3x3，共9个格子，编号如下：
  8 | 7 | 6
  ---------
  5 | 4 | 3
  ---------
  2 | 1 | 0

请分析图像，识别出当前棋盘上黑色棋子（黑色方块）和红色棋子（红色方块）分别在哪些位置。

按照如下示例的格式返回：
{
  "black_positions": [4, 6, 8],  // 比如黑色方块的位置（棋格的编号），如果没有则返回空数组
  "red_positions": [0, 2, 7]     // 比如红色方块的位置，如果没有则返回空数组
}

注意：请直接给出json格式即可
"""

    # 可用模型映射
    models = {
        # 原有模型
        "gemini-2.5-pro": "google/gemini-2.5-pro-preview",
        "gemini-2.5-pro-exp": "google/gemini-2.5-pro-exp-03-25",
        "gemma-3-27b": "google/gemma-3-27b-it:free",
        "qwen2.5-vl-32b": "qwen/qwen2.5-vl-32b-instruct:free",
        "qwen2.5-vl-72b": "qwen/qwen2.5-vl-72b-instruct:free",
        # 新增OpenAI模型
        "chatgpt-4o": "openai/chatgpt-4o-latest",
        "gpt-4o-mini": "openai/gpt-4o-mini",
        # 新增Anthropic模型
        "claude-3.7-sonnet": "anthropic/claude-3.7-sonnet"
    }
    
    # 如果传入的是模型名称映射键，转换为实际模型ID
    model_id = models.get(model_name, model_name)
    
    # 如果model_name是带前缀的模型ID，确保保留原始输入
    if "/" in model_name:
        model_id = model_name
        
    print(f"[INFO] 输入的模型名称: {model_name}")
    print(f"[INFO] 实际使用的模型ID: {model_id}")

    for attempt in range(1, max_retries + 1):
        try:
            print(f"[INFO] 第{attempt}次尝试调用模型 {model_id}")
            response = client.chat.completions.create(
                model=model_id,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_str}"
                                }
                            },
                        ],
                    }
                ],
                temperature=0.1  # 降低温度，提高一致性
            )
            content = response.choices[0].message.content
            content = content.replace("```", "").replace("json", "").strip()
            print(f"[DEBUG] 第{attempt}次模型响应:\n{content}")

            # 解析棋盘状态
            board_state = json.loads(content)
            
            # 将棋盘状态传递给其他推理模型来决定落子位置
            return board_state

        except json.JSONDecodeError:
            print(f"[WARN] 第{attempt}次解析失败，模型未返回有效 JSON。2 秒后重试...")
            await asyncio.sleep(2)
        except Exception as e:
            print(f"[ERROR] 模型调用异常（第{attempt}次）: {e}")
            await asyncio.sleep(2)

    raise HTTPException(status_code=500, detail=f"多次调用模型 {model_id} 失败，无法获得有效响应")

# ========== 机械臂动作执行 ==========
async def execute_arm_action_remote(position: int):
    global robot_connected_remote
    if not robot_connected_remote:
        # Try to connect first if not connected? Or rely on prior connect call.
        # For now, assume connect was called.
        raise HTTPException(status_code=400, detail="远程机械臂未连接或连接失败")

    request_url = f"{ARM_CONTROL_SERVICE_BASE_URL}/move"
    print(f"[DEBUG] 尝试请求远程机械臂服务: {request_url}, 数据: {{'position': {position}}}")
    try:
        response = await http_client.post(request_url, json={"position": position})
        response.raise_for_status() # Will raise an exception for 4XX/5XX responses
        result = response.json()
        print(f"[INFO] 远程机械臂服务响应 (move): {result.get('message')}")
        if response.status_code == 200 and result.get("status") == "success":
            return f"远程服务已开始在位置 {position} 执行动作: {result.get('message', '')}"
        else:
            error_detail = result.get("detail", "未知远程错误")
            print(f"[ERROR] 远程机械臂服务 (move) 返回错误: {error_detail}")
            raise HTTPException(status_code=response.status_code, detail=f"远程机械臂服务错误: {error_detail}")
    except httpx.RequestError as e:
        print(f"[ERROR] 请求远程机械臂服务 (move) 失败: {e}")
        print(f"[DEBUG] 详细错误信息: {repr(e)}")
        raise HTTPException(status_code=503, detail=f"无法连接到远程机械臂服务: {e}")
    except Exception as e: # Catch other parsing or unexpected errors
        print(f"[ERROR] 处理远程机械臂服务响应 (move) 时出错: {e}")
        print(f"[DEBUG] 详细错误信息: {repr(e)}")
        raise HTTPException(status_code=500, detail=f"处理远程机械臂响应时出错: {str(e)}")


# ========== 胜利舞蹈 ==========
async def execute_victory_dance_remote():
    global robot_connected_remote
    if not robot_connected_remote:
        raise HTTPException(status_code=400, detail="远程机械臂未连接或连接失败")

    print("🎉 请求远程机械臂服务执行胜利动作")
    try:
        response = await http_client.post(f"{ARM_CONTROL_SERVICE_BASE_URL}/victory")
        response.raise_for_status()
        result = response.json()
        print(f"[INFO] 远程机械臂服务响应 (victory): {result.get('message')}")
        if response.status_code == 200 and result.get("status") == "success":
            return f"远程服务已开始执行胜利动作: {result.get('message', '')}"
        else:
            error_detail = result.get("detail", "未知远程错误")
            print(f"[ERROR] 远程机械臂服务 (victory) 返回错误: {error_detail}")
            raise HTTPException(status_code=response.status_code, detail=f"远程机械臂服务错误: {error_detail}")
    except httpx.RequestError as e:
        print(f"[ERROR] 请求远程机械臂服务 (victory) 失败: {e}")
        raise HTTPException(status_code=503, detail=f"无法连接到远程机械臂服务: {e}")
    except Exception as e:
        print(f"[ERROR] 处理远程机械臂服务响应 (victory) 时出错: {e}")
        raise HTTPException(status_code=500, detail=f"处理远程机械臂响应时出错: {str(e)}")

# ========== API 路由 ==========

@router.on_event("startup")
async def startup_event():
    global http_client
    http_client = httpx.AsyncClient(timeout=30.0)
    # 移除健康检查逻辑和提示信息，保持函数功能但不输出任何信息

@router.on_event("shutdown")
async def shutdown_event():
    await http_client.aclose()

@router.get("/health")
async def health_check():
    """健康检查接口 (本地服务)"""
    # 不再尝试连接远程机械臂服务，直接返回本地状态
    return {
        "status": "ok", 
        "local_robot_direct_control": False,
        "remote_arm_service_url": ARM_CONTROL_SERVICE_BASE_URL,
        "remote_arm_service_status": "disabled", # 健康检查已禁用
        "remote_arm_connected": robot_connected_remote # 使用本地记录的连接状态
    }

@router.post("/connect")
async def connect_robot_remote(connection: RobotConnection):
    """连接到远程机械臂服务接口"""
    global robot_connected_remote, ARM_CONTROL_SERVICE_BASE_URL
    
    # 每次连接时重新从环境变量读取地址，确保使用最新设置的地址
    ARM_CONTROL_SERVICE_BASE_URL = os.getenv("ARM_SERVICE_URL", ARM_CONTROL_SERVICE_BASE_URL)
    
    # 强制使用固定的从臂串口 /dev/ttyACM0
    fixed_port = "/dev/ttyACM0"
    print(f"[INFO] 尝试连接到远程机械臂服务: {ARM_CONTROL_SERVICE_BASE_URL} 使用固定从臂串口 {fixed_port}")
    
    try:
        response = await http_client.post(f"{ARM_CONTROL_SERVICE_BASE_URL}/connect", json={"port": fixed_port})
        response.raise_for_status() # Raise HTTPStatusError for bad responses (4xx or 5xx)
        result = response.json()
        
        if response.status_code == 200 and result.get("status") == "success":
            robot_connected_remote = True
            print(f"[INFO] 成功连接到远程机械臂: {result.get('message')}")
            return {"status": "success", "message": result.get("message")}
        else:
            robot_connected_remote = False
            error_detail = result.get("detail", result.get("message", "连接失败但未提供详细信息"))
            print(f"[ERROR] 远程机械臂服务返回连接错误: {error_detail}")
            raise HTTPException(status_code=response.status_code, detail=f"远程机械臂服务错误: {error_detail}")

    except httpx.TimeoutException:
        robot_connected_remote = False
        print(f"[ERROR] 连接远程机械臂服务超时 ({ARM_CONTROL_SERVICE_BASE_URL})")
        raise HTTPException(status_code=504, detail="连接远程机械臂服务超时")
    except httpx.RequestError as e:
        robot_connected_remote = False
        print(f"[ERROR] 请求远程机械臂服务 (connect) 失败: {e}")
        raise HTTPException(status_code=503, detail=f"无法连接到远程机械臂服务: {e}")
    except Exception as e: # Catch other parsing or unexpected errors
        robot_connected_remote = False
        print(f"[ERROR] 处理远程机械臂服务响应 (connect) 时出错: {e}")
        raise HTTPException(status_code=500, detail=f"处理远程机械臂响应时出错: {str(e)}")


@router.post("/play")
async def play_move(request: Request, background_tasks: BackgroundTasks, capture: CameraCapture = Body(CameraCapture()), model: str = "gemini-2.5-pro", inference_model: str = "deepseek-chat"):
    """拍照并通过远程机械臂执行动作接口"""
    global robot_connected_remote
    if not robot_connected_remote:
        raise HTTPException(status_code=400, detail="远程机械臂未连接。请先调用 /connect 端点。")
    
    try:
        # 获取请求头中的模型参数(优先级更高)
        headers = request.headers
        header_model = headers.get("X-Selected-Model")
        header_inference = headers.get("X-Inference-Model")
        
        # 确定要使用的模型
        vision_model = header_model or model
        infer_model = header_inference or inference_model
        
        print(f"[INFO] 接收到游戏请求:")
        print(f"[INFO] - 请求头视觉模型参数: {header_model}")
        print(f"[INFO] - 请求头推理模型参数: {header_inference}")
        print(f"[INFO] - 查询参数视觉模型: {model}")
        print(f"[INFO] - 查询参数推理模型: {inference_model}")
        print(f"[INFO] - 最终使用视觉模型: {vision_model}")
        print(f"[INFO] - 最终使用推理模型: {infer_model}")
        
        print("拍照中...")
        img_b64 = capture_and_encode_base64(capture.camera_id)
        
        # 第一步：使用视觉模型识别棋子位置
        print("识别棋盘状态...")
        board_state = await analyze_board_from_base64(img_b64, vision_model)
        
        # 第二步：使用纯文本推理模型决定最佳落子位置
        print("推理最佳落子位置...")
        action_result = await infer_best_move(board_state, infer_model)
        
        # 执行动作
        if action_result["action"] == "move":
            # 执行机械臂动作
            position = action_result["position"]
            print(f"[INFO] 准备通过远程服务执行落子动作：位置 {position}")
            
            action_message = ""
            try:
                action_message = await execute_arm_action_remote(position)
                print(f"[INFO] 远程落子执行结果: {action_message}")
            except HTTPException as e: # Catch HTTP exceptions from remote call
                 print(f"[ERROR] 远程执行落子动作失败: {e.detail}")
                 # Decide if this should fail the whole /play or return a specific status
                 raise HTTPException(status_code=e.status_code, detail=f"远程执行落子动作失败: {e.detail}")
            except Exception as action_error: # Catch any other unexpected error
                print(f"[ERROR] 执行远程落子动作时发生意外错误: {action_error}")
                raise HTTPException(status_code=500, detail=f"执行远程落子动作时发生意外错误: {str(action_error)}")

            response = {
                "status": "success", 
                "board_state": board_state,
                "action": "move", 
                "position": position,
                "reason": action_result.get("reason", ""),
                "vision_model": vision_model,
                "inference_model": infer_model
            }
        elif action_result["action"] == "win":
            # 不再执行胜利动作，只返回胜利信息
            print(f"[INFO] 检测到机械臂胜利，但不执行胜利动作")
                
            response = {
                "status": "success", 
                "board_state": board_state,
                "action": "win", 
                "reason": action_result.get("reason", "机械臂胜利"),
                "vision_model": vision_model,
                "inference_model": infer_model
            }
        elif action_result["action"] == "draw":
            response = {
                "status": "success", 
                "board_state": board_state,
                "action": "draw", 
                "reason": action_result.get("reason", "游戏平局"),
                "vision_model": vision_model,
                "inference_model": infer_model
            }
        elif action_result["action"] == "fail":
            response = {
                "status": "success", 
                "board_state": board_state,
                "action": "fail", 
                "reason": action_result.get("reason", "人类胜利"),
                "vision_model": vision_model,
                "inference_model": infer_model
            }
        else:
            response = {
                "status": "error", 
                "board_state": board_state,
                "message": f"未知动作: {action_result['action']}",
                "vision_model": vision_model,
                "inference_model": infer_model
            }
        
        return response
        
    except Exception as e:
        print(f"[ERROR] 处理请求时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/capture")
async def capture_image(camera_id: int = 2):
    """拍照接口"""
    try:
        img_b64 = capture_and_encode_base64(camera_id)
        return {
            "status": "success",
            "image": img_b64
        }
    except Exception as e:
        print(f"[ERROR] 拍照时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/analyze")
async def analyze_image(request: Request, model_info: AnalyzeRequest, model: str = "gemini-2.5-pro", inference_model: str = "deepseek/deepseek-chat-v3-0324"):
    """分析图像接口"""
    try:
        # 获取请求头中的模型参数(优先级更高)
        headers = request.headers
        header_model = headers.get("X-Selected-Model")
        header_inference = headers.get("X-Inference-Model")
        
        # 确定要使用的模型
        vision_model = header_model or model_info.model or model
        infer_model = header_inference or getattr(model_info, 'inference_model', None) or inference_model
        
        print(f"[INFO] 接收到分析请求:")
        print(f"[INFO] - 请求头视觉模型参数: {header_model}")
        print(f"[INFO] - 请求体视觉模型参数: {getattr(model_info, 'model', None)}")
        print(f"[INFO] - 查询参数视觉模型: {model}")
        print(f"[INFO] - 最终使用视觉模型: {vision_model}")
        print(f"[INFO] - 使用的推理模型: {infer_model}")
        
        # 第一步：使用视觉模型识别棋子位置
        board_state = await analyze_board_from_base64(model_info.image, vision_model)
        
        # 第二步：使用纯文本推理模型决定最佳落子位置
        action_result = await infer_best_move(board_state, infer_model)
        
        return {
            "status": "success",
            "board_state": board_state,  # 返回棋盘状态
            "result": action_result,     # 返回推理结果
            "vision_model": vision_model, # 返回使用的视觉模型
            "inference_model": infer_model # 返回使用的推理模型
        }
    except Exception as e:
        print(f"[ERROR] 分析图像时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/move")
async def execute_move_remote_endpoint(request: Request, move: ChessMove, background_tasks: BackgroundTasks):
    """手动通过远程服务执行落子动作接口"""
    global robot_connected_remote
    if not robot_connected_remote:
        raise HTTPException(status_code=400, detail="远程机械臂未连接。请先调用 /connect 端点。")
    
    # 获取请求中的模型参数，用于日志记录
    headers = request.headers
    model_logging = getattr(move, 'model', None) or headers.get("X-Selected-Model", "默认模型(日志)")
    print(f"[INFO] 手动执行远程落子动作，记录模型: {model_logging}")
    
    try:
        position = move.position
        action_result_msg = await execute_arm_action_remote(position)
        return {
            "status": "success",
            "message": f"请求已发送至远程服务，在位置 {position} 开始落子",
            "result": action_result_msg, # Message from remote service call
            "model_used_for_logging": model_logging
        }
    except HTTPException as e: # Propagate HTTP exceptions from remote call
        raise e
    except Exception as e:
        print(f"[ERROR] 执行远程落子动作时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/victory")
async def execute_victory_remote_endpoint(background_tasks: BackgroundTasks):
    """通过远程服务执行胜利动作接口"""
    global robot_connected_remote
    if not robot_connected_remote:
        raise HTTPException(status_code=400, detail="远程机械臂未连接。请先调用 /connect 端点。")
    
    # 不再执行胜利动作，只记录日志并返回成功响应
    print("[INFO] 收到胜利动作请求，但根据需求不执行实际动作")
    
    return {
        "status": "success",
        "message": "根据需求配置，不执行胜利动作，仅在前端显示胜利信息"
    }

# 视频流API
@router.post("/video/start")
async def start_video_stream(camera_id: int = 2):
    """启动视频流"""
    global video_capture
    
    # 检查视频流是否已经初始化，避免重复初始化
    if video_capture is not None and video_capture.isOpened():
        print("[INFO] 视频流已存在，无需重新初始化")
        return {"status": "success", "message": "视频流已在运行中"}
        
    if init_video_stream(camera_id):
        return {"status": "success", "message": f"成功启动视频流，摄像头ID: {camera_id}"}
    else:
        raise HTTPException(status_code=500, detail="启动视频流失败")

@router.get("/video/frame")
async def get_frame():
    """获取当前视频帧"""
    global video_capture, robot_connected_remote
    
    # 添加连接状态检查，如果机械臂未连接，则拒绝处理视频流请求
    if not robot_connected_remote:
        print("[WARN] 机械臂未连接，拒绝视频流请求")
        raise HTTPException(status_code=403, detail="机械臂未连接，请先连接机械臂")
    
    # 记录连续失败次数，避免无限重试
    failure_count = 0
    max_failures = 2
    
    # 如果视频流未初始化或已关闭，尝试初始化
    if video_capture is None or not video_capture.isOpened():
        # 防止频繁重复初始化视频流
        if failure_count == 0:
            print("[WARN] 视频流未初始化或已关闭，尝试自动初始化...")
            # 确保之前的摄像头资源被释放
            close_video_stream()
            await asyncio.sleep(0.5)
            if not init_video_stream(2):  # 默认使用摄像头2
                failure_count += 1
                if failure_count > max_failures:
                    raise HTTPException(status_code=503, detail="视频流无法初始化")
        else:
            raise HTTPException(status_code=503, detail="视频流未初始化且上次初始化失败")
    
    # 多次尝试获取帧
    for attempt in range(2):
        frame = get_video_frame()
        if frame:
            return {"status": "success", "image": frame}
        
        # 短暂等待后重试，避免过于频繁的请求
        await asyncio.sleep(0.1)
    
    # 尝试获取帧失败，但不要频繁重新初始化视频流
    # 只有当视频流确实已关闭时才重新初始化
    if video_capture is None or not video_capture.isOpened():
        print("[WARN] 视频流异常，尝试重新初始化...")
        close_video_stream()  # 确保资源释放
        await asyncio.sleep(0.5)
        
        if init_video_stream(2):
            # 再次尝试获取帧
            await asyncio.sleep(0.3)  # 等待摄像头稳定
    frame = get_video_frame()
    if frame:
        return {"status": "success", "image": frame}
    
    # 返回错误但不中断前端请求，允许前端继续尝试
    raise HTTPException(status_code=500, detail="获取视频帧失败，请稍后重试")

@router.post("/video/stop")
async def stop_video_stream():
    """停止视频流"""
    close_video_stream()
    return {"status": "success", "message": "视频流已停止"}

@router.post("/disconnect")
async def disconnect_robot_remote():
    """断开远程机械臂连接"""
    global robot_connected_remote, video_capture
    
    # 首先关闭视频流，防止后续仍有视频流请求
    print("[INFO] 断开连接前先关闭视频流")
    close_video_stream()
    
    # 强制将连接状态设为断开，即使后续操作失败
    # 这样可以确保前端不会继续请求视频帧
    robot_connected_remote = False
    
    if not robot_connected_remote: # If we think it's not connected, still try to tell remote to disconnect
        print("[INFO] 本地记录远程机械臂未连接，但仍尝试发送断开指令到远程服务。")
        # Allow proceeding to send disconnect, as remote might be in a different state.

    try:
        # 尝试断开远程连接，使用短超时避免永久阻塞
        print(f"[INFO] 请求远程机械臂服务断开连接: {ARM_CONTROL_SERVICE_BASE_URL}/disconnect")
        disconnect_client = httpx.AsyncClient(timeout=10.0)  # 使用10秒短超时
        try:
            response = await disconnect_client.post(f"{ARM_CONTROL_SERVICE_BASE_URL}/disconnect")
            response.raise_for_status()
            result = response.json()
            
            # 处理响应...
        except httpx.TimeoutException:
            print("[WARN] 断开连接请求超时，但仍将本地状态设为已断开")
        finally:
            await disconnect_client.aclose()  # 确保客户端关闭
        
        # 正常返回路径
        if 'response' in locals() and 'result' in locals() and response.status_code == 200 and result.get("status") == "success":
            print(f"[INFO] 远程机械臂服务响应 (disconnect): {result.get('message')}")
            return {"status": "success", "message": result.get("message")}
        else:
            # 即使远程服务未确认，我们也已经重置了本地状态
            error_detail = "未收到远程服务成功响应"
            if 'result' in locals():
                error_detail = result.get("detail", result.get("message", error_detail))
            print(f"[WARN] 远程机械臂服务在断开时报告问题: {error_detail}")
            # 仍然返回成功，因为本地状态已重置
            return {"status": "success", "message": f"已向远程服务发送断开指令。远程服务响应: {error_detail}"}

    except httpx.RequestError as e:
        print(f"[ERROR] 请求远程机械臂服务 (disconnect) 失败: {e}")
        return {"status": "success", "message": f"无法连接到远程机械臂服务以断开: {e}. 本地状态已更新为断开连接。"}
    except Exception as e:
        print(f"[ERROR] 处理远程机械臂服务响应 (disconnect) 时出错: {e}")
        return {"status": "success", "message": f"处理远程机械臂断开响应时出错: {str(e)}. 本地状态已更新为断开连接。"}

# 添加推理最佳落子位置的函数
async def infer_best_move(board_state, model_name: str = "deepseek/deepseek-chat-v3-0324"):
    """使用纯文本模型推理最佳落子位置"""
    
    # 可用推理模型映射
    models = {
        "deepseek-chat": "deepseek/deepseek-chat-v3-0324",
        "claude-3-5-sonnet": "anthropic/claude-3-5-sonnet-20240620:free",
        "claude-3-7-sonnet": "anthropic/claude-3.7-sonnet",
        "llama-3-70b": "meta-llama/llama-3-70b-instruct:free"
    }
    
    # 获取实际模型ID
    model_id = models.get(model_name, model_name)
    
    # 构建提示词
    black_positions = board_state.get("black_positions", [])
    red_positions = board_state.get("red_positions", [])
    available_positions = [i for i in range(9) if i not in black_positions and i not in red_positions]
    
    prompt = f"""
你是一个下井字棋的高手，您需要分析井字棋棋盘状态并决定最佳落子位置。


以下是游戏规则:
- 玩家轮流在3乘3的格上放置自己的棋子
- 最先以横、直、对角线连成一线则为胜（三个相同的棋子）
以下情况可以判断为赢：
横线的情况：[0,1,2],[3,4,5],[6,7,8]
直线的情况：[0,3,6],[1,4,7],[2,5,8]
对角线情况：[0,4,8],[2,4,6]
- 如果棋盘填满而没有玩家获胜，则游戏平局

棋盘为3x3，共9个格子，编号如下：
  8 | 7 | 6
  ---------
  5 | 4 | 3
  ---------
  2 | 1 | 0

当前人类棋子已经占了编号: {black_positions}的棋格
当前机械臂棋子已经占了编号: {red_positions}的棋格
可用位置: {available_positions}
- 当前轮到（机械臂）下棋，你需要分析当前局面，并选择最佳落子位置目标是取胜或者阻止人类获胜，
比如：人类的棋子已经占据编号为2、4的棋格。你要阻止人类形成2,4,6形成对角线的棋局，所以你要落子在编号为6的棋格。
  "action": "move", 
  "position": 6,    
  "reason": "人类棋子已经占了编号3和4的棋格，要阻止人类形成2,4,6的对角线的棋局
示例数据： 
  "action": "move", 
  "position": 5,    
  "reason": "人类棋子已经占了编号3和4的棋格，要阻止人类形成3,4,5的横线棋局

现在请以机械臂的角度分析当前局面，并选择最佳落子位置：
1. 无论哪一方获胜，请返回获胜位置和胜利原因
2. 如果人类即将获胜，请返回阻止人类获胜的位置

请严格按照以下JSON格式输出：
{{
  "action": "move", // 可能的值: "move"(落子), "win"(机械臂已胜利), "draw"(平局), "fail"(人类已胜利)
  "position": 4,    // 推荐的落子位置，如果是move动作才需要
  "reason": "最佳落子理由" // 解释选择该位置的原因
}}

"""

    try:
        print(f"[INFO] 调用推理模型 {model_id} 分析最佳落子位置")
        response = client.chat.completions.create(
            model=model_id,
            messages=[
                {"role": "user", "content": prompt}
            ],
            temperature=0.7
        )
        content = response.choices[0].message.content
        content = content.replace("```", "").replace("json", "").strip()
        print(f"[DEBUG] 推理模型响应:\n{content}")
        try:
            action_result = json.loads(content)
            return action_result
        except json.JSONDecodeError:
            print("[警告] 原始 JSON 解析失败，尝试使用正则提取 JSON")
            # 尝试用正则提取第一个合法的 JSON 对象
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                try:
                    action_result=json_match.group()
                    action_result=json.loads(action_result)
                    print(f"[DEBUG] 正则提取的 JSON:\n{action_result}")
                    return action_result
                except json.JSONDecodeError as e2:
                    print(f"[错误] 正则提取的内容也无法解析：{e2}")
    except Exception as e:
        print(f"[ERROR] 推理模型调用失败: {e}")
        # 如果推理失败，提供一个基本的决策
        return make_fallback_decision(board_state)

def make_fallback_decision(board_state):
    """如果推理模型调用失败，提供一个基本的决策逻辑"""
    # 获取棋盘状态
    black_positions = set(board_state.get("black_positions", []))
    red_positions = set(board_state.get("red_positions", []))
    all_positions = black_positions.union(red_positions)
    available_positions = [i for i in range(9) if i not in all_positions]
    
    # 如果没有可用位置，返回平局
    if not available_positions:
        return {
            "action": "draw",
            "reason": "棋盘已满，游戏平局"
        }
    
    # 如果中央位置可用，优先选择
    if 4 in available_positions:
        return {
            "action": "move",
            "position": 4,
            "reason": "中央位置通常是最优选择"
        }
    
    # 否则选择第一个可用位置
    position = available_positions[0]
    return {
        "action": "move",
        "position": position,
        "reason": "基本策略，选择可用位置"
    } 