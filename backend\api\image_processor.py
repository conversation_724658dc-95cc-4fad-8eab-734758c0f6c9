#!/usr/bin/env python
# 图像处理模块
# 提供图像压缩、格式转换、base64编码等功能

import base64
import io
import os
from typing import Tuple, Optional, Union, Dict, Any
from PIL import Image, ImageOps
import numpy as np
from pathlib import Path

class ImageProcessor:
    """图像处理器"""
    
    # 支持的图像格式
    SUPPORTED_FORMATS = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    
    # 默认配置
    DEFAULT_MAX_SIZE = 1024
    DEFAULT_QUALITY = 85
    DEFAULT_FORMAT = 'JPEG'
    
    @staticmethod
    def is_supported_format(file_path: Union[str, Path]) -> bool:
        """检查文件格式是否支持
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否支持该格式
        """
        suffix = Path(file_path).suffix.lower()
        return suffix in ImageProcessor.SUPPORTED_FORMATS
    
    @staticmethod
    def get_image_info(image_path: Union[str, Path]) -> Dict[str, Any]:
        """获取图像信息
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            图像信息字典
        """
        try:
            with Image.open(image_path) as img:
                return {
                    'width': img.width,
                    'height': img.height,
                    'mode': img.mode,
                    'format': img.format,
                    'size_bytes': os.path.getsize(image_path),
                    'has_transparency': img.mode in ('RGBA', 'LA', 'P') and 'transparency' in img.info
                }
        except Exception as e:
            raise ValueError(f"无法读取图像信息: {e}")
    
    @staticmethod
    def compress_image(
        image_path: Union[str, Path],
        max_size: int = DEFAULT_MAX_SIZE,
        quality: int = DEFAULT_QUALITY,
        output_format: str = DEFAULT_FORMAT,
        maintain_aspect_ratio: bool = True
    ) -> bytes:
        """压缩图像
        
        Args:
            image_path: 输入图像路径
            max_size: 最大尺寸（像素）
            quality: 压缩质量（1-100）
            output_format: 输出格式（JPEG, PNG, WEBP）
            maintain_aspect_ratio: 是否保持宽高比
            
        Returns:
            压缩后的图像字节数据
        """
        try:
            with Image.open(image_path) as img:
                # 处理图像方向（EXIF）
                img = ImageOps.exif_transpose(img)
                
                # 转换颜色模式
                if output_format.upper() == 'JPEG' and img.mode in ('RGBA', 'LA', 'P'):
                    # JPEG不支持透明度，转换为RGB
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                elif output_format.upper() == 'PNG' and img.mode not in ('RGBA', 'RGB', 'L'):
                    img = img.convert('RGBA')
                
                # 调整尺寸
                if max_size > 0:
                    img = ImageProcessor._resize_image(img, max_size, maintain_aspect_ratio)
                
                # 保存到字节流
                buffer = io.BytesIO()
                save_kwargs = {'format': output_format.upper()}
                
                if output_format.upper() == 'JPEG':
                    save_kwargs.update({
                        'quality': quality,
                        'optimize': True,
                        'progressive': True
                    })
                elif output_format.upper() == 'PNG':
                    save_kwargs.update({
                        'optimize': True,
                        'compress_level': 9
                    })
                elif output_format.upper() == 'WEBP':
                    save_kwargs.update({
                        'quality': quality,
                        'optimize': True
                    })
                
                img.save(buffer, **save_kwargs)
                return buffer.getvalue()
                
        except Exception as e:
            raise ValueError(f"图像压缩失败: {e}")
    
    @staticmethod
    def _resize_image(img: Image.Image, max_size: int, maintain_aspect_ratio: bool) -> Image.Image:
        """调整图像尺寸
        
        Args:
            img: PIL图像对象
            max_size: 最大尺寸
            maintain_aspect_ratio: 是否保持宽高比
            
        Returns:
            调整后的图像
        """
        width, height = img.size
        
        if max(width, height) <= max_size:
            return img
        
        if maintain_aspect_ratio:
            # 保持宽高比
            if width > height:
                new_width = max_size
                new_height = int(height * max_size / width)
            else:
                new_height = max_size
                new_width = int(width * max_size / height)
        else:
            # 不保持宽高比，直接缩放到指定尺寸
            new_width = new_height = max_size
        
        return img.resize((new_width, new_height), Image.Resampling.LANCZOS)
    
    @staticmethod
    def image_to_base64(
        image_path: Union[str, Path],
        max_size: int = DEFAULT_MAX_SIZE,
        quality: int = DEFAULT_QUALITY,
        output_format: str = DEFAULT_FORMAT,
        include_data_url: bool = True
    ) -> str:
        """将图像转换为base64编码
        
        Args:
            image_path: 图像文件路径
            max_size: 最大尺寸
            quality: 压缩质量
            output_format: 输出格式
            include_data_url: 是否包含data URL前缀
            
        Returns:
            base64编码的图像字符串
        """
        try:
            # 压缩图像
            image_bytes = ImageProcessor.compress_image(
                image_path, max_size, quality, output_format
            )
            
            # 转换为base64
            base64_str = base64.b64encode(image_bytes).decode('utf-8')
            
            if include_data_url:
                mime_type = f"image/{output_format.lower()}"
                return f"data:{mime_type};base64,{base64_str}"
            else:
                return base64_str
                
        except Exception as e:
            raise ValueError(f"图像base64转换失败: {e}")
    
    @staticmethod
    def base64_to_image(base64_str: str, output_path: Union[str, Path]) -> None:
        """将base64字符串保存为图像文件
        
        Args:
            base64_str: base64编码的图像字符串
            output_path: 输出文件路径
        """
        try:
            # 处理data URL格式
            if base64_str.startswith('data:'):
                base64_str = base64_str.split(',', 1)[1]
            
            # 解码base64
            image_bytes = base64.b64decode(base64_str)
            
            # 保存文件
            with open(output_path, 'wb') as f:
                f.write(image_bytes)
                
        except Exception as e:
            raise ValueError(f"base64图像保存失败: {e}")
    
    @staticmethod
    def create_thumbnail(
        image_path: Union[str, Path],
        thumbnail_size: Tuple[int, int] = (200, 200),
        output_path: Optional[Union[str, Path]] = None
    ) -> Union[bytes, None]:
        """创建缩略图
        
        Args:
            image_path: 原图像路径
            thumbnail_size: 缩略图尺寸 (width, height)
            output_path: 输出路径，如果为None则返回字节数据
            
        Returns:
            如果output_path为None，返回缩略图字节数据；否则返回None
        """
        try:
            with Image.open(image_path) as img:
                # 处理图像方向
                img = ImageOps.exif_transpose(img)
                
                # 创建缩略图（保持宽高比）
                img.thumbnail(thumbnail_size, Image.Resampling.LANCZOS)
                
                # 如果是RGBA模式，转换为RGB
                if img.mode == 'RGBA':
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1])
                    img = background
                
                if output_path:
                    # 保存到文件
                    img.save(output_path, 'JPEG', quality=85, optimize=True)
                    return None
                else:
                    # 返回字节数据
                    buffer = io.BytesIO()
                    img.save(buffer, 'JPEG', quality=85, optimize=True)
                    return buffer.getvalue()
                    
        except Exception as e:
            raise ValueError(f"缩略图创建失败: {e}")
    
    @staticmethod
    def validate_image(image_path: Union[str, Path]) -> Tuple[bool, str]:
        """验证图像文件
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            path = Path(image_path)
            
            # 检查文件是否存在
            if not path.exists():
                return False, "文件不存在"
            
            # 检查文件大小
            file_size = path.stat().st_size
            if file_size == 0:
                return False, "文件为空"
            
            # 检查文件格式
            if not ImageProcessor.is_supported_format(path):
                return False, f"不支持的文件格式: {path.suffix}"
            
            # 尝试打开图像
            with Image.open(path) as img:
                # 验证图像完整性
                img.verify()
            
            # 重新打开获取基本信息（verify后图像对象不可用）
            with Image.open(path) as img:
                width, height = img.size
                if width <= 0 or height <= 0:
                    return False, "图像尺寸无效"
            
            return True, "图像有效"
            
        except Exception as e:
            return False, f"图像验证失败: {e}"
