#!/usr/bin/env python
# Web界面API模块
# 提供图像上传和AI交流的Web端口

import os
import uuid
import shutil
from pathlib import Path
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Depends
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel
import asyncio

from .doubao_client import DoubaoClient
from .ai_robot_controller import AIRobotController
from .image_processor import ImageProcessor
from .robot_arm import robot_client, client_lock

# 创建路由器
router = APIRouter()

# 全局变量
ai_controller: Optional[AIRobotController] = None
upload_dir = Path("uploads")
upload_dir.mkdir(exist_ok=True)

# 请求模型
class ChatRequest(BaseModel):
    message: str
    image_id: Optional[str] = None

class TaskRequest(BaseModel):
    query: str
    image_id: str

class FeedbackRequest(BaseModel):
    image_id: str

# 响应模型
class ChatResponse(BaseModel):
    success: bool
    response: Optional[str] = None
    error: Optional[str] = None
    conversation_id: Optional[str] = None

class TaskResponse(BaseModel):
    success: bool
    stage: Optional[str] = None
    response: Optional[str] = None
    parsed_data: Optional[Dict[str, Any]] = None
    execution: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

# 依赖函数
def get_ai_controller() -> AIRobotController:
    """获取AI控制器实例"""
    global ai_controller
    if ai_controller is None:
        doubao_client = DoubaoClient()
        ai_controller = AIRobotController(doubao_client=doubao_client)
        
        # 如果有机械臂客户端，设置到控制器中
        if robot_client is not None:
            ai_controller.set_robot_client(robot_client)
    
    return ai_controller

@router.get("/", response_class=HTMLResponse)
async def get_web_interface():
    """获取Web界面HTML"""
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>AI机械臂控制系统</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                background: white;
                border-radius: 10px;
                padding: 30px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #333;
                text-align: center;
                margin-bottom: 30px;
            }
            .section {
                margin-bottom: 30px;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 8px;
                background-color: #fafafa;
            }
            .section h2 {
                color: #555;
                margin-top: 0;
            }
            .upload-area {
                border: 2px dashed #ccc;
                border-radius: 8px;
                padding: 40px;
                text-align: center;
                cursor: pointer;
                transition: border-color 0.3s;
            }
            .upload-area:hover {
                border-color: #007bff;
            }
            .upload-area.dragover {
                border-color: #007bff;
                background-color: #f0f8ff;
            }
            input[type="file"] {
                display: none;
            }
            .btn {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                margin: 5px;
            }
            .btn:hover {
                background-color: #0056b3;
            }
            .btn:disabled {
                background-color: #ccc;
                cursor: not-allowed;
            }
            .btn-danger {
                background-color: #dc3545;
            }
            .btn-danger:hover {
                background-color: #c82333;
            }
            textarea {
                width: 100%;
                min-height: 100px;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 5px;
                font-size: 14px;
                resize: vertical;
            }
            .response-area {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 15px;
                min-height: 200px;
                white-space: pre-wrap;
                font-family: monospace;
                font-size: 12px;
                overflow-y: auto;
                max-height: 400px;
            }
            .image-preview {
                max-width: 300px;
                max-height: 300px;
                border: 1px solid #ddd;
                border-radius: 5px;
                margin: 10px 0;
            }
            .status {
                padding: 10px;
                border-radius: 5px;
                margin: 10px 0;
            }
            .status.success {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            .status.error {
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            .status.info {
                background-color: #d1ecf1;
                color: #0c5460;
                border: 1px solid #bee5eb;
            }
            .grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
            }
            @media (max-width: 768px) {
                .grid {
                    grid-template-columns: 1fr;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 AI机械臂控制系统</h1>
            
            <!-- 系统状态 -->
            <div class="section">
                <h2>系统状态</h2>
                <div id="systemStatus" class="status info">正在检查系统状态...</div>
                <button class="btn" onclick="checkSystemStatus()">刷新状态</button>
                <button class="btn btn-danger" onclick="emergencyStop()">紧急停止</button>
            </div>
            
            <div class="grid">
                <!-- 图像上传区域 -->
                <div class="section">
                    <h2>图像上传</h2>
                    <div class="upload-area" onclick="document.getElementById('imageInput').click()">
                        <p>点击或拖拽图片到此处上传</p>
                        <p style="font-size: 12px; color: #666;">支持 JPG, PNG, BMP, TIFF, WEBP 格式</p>
                    </div>
                    <input type="file" id="imageInput" accept="image/*" onchange="uploadImage(this)">
                    <div id="imagePreview"></div>
                    <div id="uploadStatus"></div>
                </div>
                
                <!-- 任务控制区域 -->
                <div class="section">
                    <h2>任务控制</h2>
                    <textarea id="taskQuery" placeholder="输入任务描述，例如：请抓取桌上的红色方块"></textarea>
                    <br>
                    <button class="btn" onclick="startTask()" id="startTaskBtn">开始任务</button>
                    <button class="btn" onclick="sendFeedback()" id="feedbackBtn" disabled>发送反馈图像</button>
                    <button class="btn" onclick="resetTask()">重置任务</button>
                </div>
            </div>
            
            <!-- AI响应区域 -->
            <div class="section">
                <h2>AI响应</h2>
                <div id="aiResponse" class="response-area">等待AI响应...</div>
            </div>
            
            <!-- 执行日志 -->
            <div class="section">
                <h2>执行日志</h2>
                <div id="executionLog" class="response-area">系统就绪...</div>
            </div>
        </div>
        
        <script>
            let currentImageId = null;
            let systemReady = false;
            
            // 页面加载时检查系统状态
            window.onload = function() {
                checkSystemStatus();
                setupDragAndDrop();
            };
            
            // 设置拖拽上传
            function setupDragAndDrop() {
                const uploadArea = document.querySelector('.upload-area');
                
                uploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });
                
                uploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                });
                
                uploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                    
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        uploadImageFile(files[0]);
                    }
                });
            }
            
            // 检查系统状态
            async function checkSystemStatus() {
                try {
                    const response = await fetch('/web/status');
                    const data = await response.json();
                    
                    const statusDiv = document.getElementById('systemStatus');
                    if (data.success) {
                        const status = data.status;
                        let statusText = `AI连接: ${status.ai_connected ? '✅' : '❌'} | `;
                        statusText += `机械臂连接: ${status.robot_connected ? '✅' : '❌'} | `;
                        statusText += `机械臂运行: ${status.robot_running ? '✅' : '❌'}`;
                        
                        if (status.current_stage) {
                            statusText += ` | 当前阶段: ${status.current_stage}`;
                        }
                        
                        statusDiv.textContent = statusText;
                        statusDiv.className = 'status ' + (status.ai_connected && status.robot_connected ? 'success' : 'error');
                        systemReady = status.ai_connected && status.robot_connected;
                    } else {
                        statusDiv.textContent = '系统状态检查失败: ' + data.error;
                        statusDiv.className = 'status error';
                        systemReady = false;
                    }
                } catch (error) {
                    document.getElementById('systemStatus').textContent = '无法连接到服务器';
                    document.getElementById('systemStatus').className = 'status error';
                    systemReady = false;
                }
            }
            
            // 上传图像
            function uploadImage(input) {
                if (input.files && input.files[0]) {
                    uploadImageFile(input.files[0]);
                }
            }
            
            async function uploadImageFile(file) {
                const formData = new FormData();
                formData.append('file', file);
                
                const statusDiv = document.getElementById('uploadStatus');
                statusDiv.innerHTML = '<div class="status info">正在上传图像...</div>';
                
                try {
                    const response = await fetch('/web/upload', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        currentImageId = data.image_id;
                        
                        // 显示图像预览
                        const preview = document.getElementById('imagePreview');
                        preview.innerHTML = `<img src="/web/image/${data.image_id}" class="image-preview" alt="上传的图像">`;
                        
                        statusDiv.innerHTML = '<div class="status success">图像上传成功</div>';
                        
                        // 启用相关按钮
                        document.getElementById('startTaskBtn').disabled = false;
                        document.getElementById('feedbackBtn').disabled = false;
                        
                        addLog('图像上传成功: ' + data.filename);
                    } else {
                        statusDiv.innerHTML = '<div class="status error">上传失败: ' + data.error + '</div>';
                        addLog('图像上传失败: ' + data.error);
                    }
                } catch (error) {
                    statusDiv.innerHTML = '<div class="status error">上传失败: ' + error.message + '</div>';
                    addLog('图像上传异常: ' + error.message);
                }
            }
            
            // 开始任务
            async function startTask() {
                if (!currentImageId) {
                    alert('请先上传图像');
                    return;
                }
                
                if (!systemReady) {
                    alert('系统未就绪，请检查连接状态');
                    return;
                }
                
                const query = document.getElementById('taskQuery').value.trim();
                if (!query) {
                    alert('请输入任务描述');
                    return;
                }
                
                const btn = document.getElementById('startTaskBtn');
                btn.disabled = true;
                btn.textContent = '执行中...';
                
                try {
                    const response = await fetch('/web/task', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            query: query,
                            image_id: currentImageId
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        document.getElementById('aiResponse').textContent = data.response || JSON.stringify(data.parsed_data, null, 2);
                        
                        let logMessage = `任务开始 - 阶段: ${data.stage}`;
                        if (data.execution) {
                            logMessage += ` | 执行: ${data.execution.success ? '成功' : '失败'} (${data.execution.message})`;
                        }
                        addLog(logMessage);
                    } else {
                        document.getElementById('aiResponse').textContent = '任务执行失败: ' + data.error;
                        addLog('任务执行失败: ' + data.error);
                    }
                } catch (error) {
                    document.getElementById('aiResponse').textContent = '请求失败: ' + error.message;
                    addLog('任务请求异常: ' + error.message);
                } finally {
                    btn.disabled = false;
                    btn.textContent = '开始任务';
                }
            }
            
            // 发送反馈图像
            async function sendFeedback() {
                if (!currentImageId) {
                    alert('请先上传反馈图像');
                    return;
                }
                
                const btn = document.getElementById('feedbackBtn');
                btn.disabled = true;
                btn.textContent = '分析中...';
                
                try {
                    const response = await fetch('/web/feedback', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            image_id: currentImageId
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        document.getElementById('aiResponse').textContent = data.response || JSON.stringify(data.parsed_data, null, 2);
                        addLog(`反馈分析完成 - 阶段: ${data.stage} | 状态: ${data.parsed_data?.status || '未知'}`);
                    } else {
                        document.getElementById('aiResponse').textContent = '反馈分析失败: ' + data.error;
                        addLog('反馈分析失败: ' + data.error);
                    }
                } catch (error) {
                    document.getElementById('aiResponse').textContent = '请求失败: ' + error.message;
                    addLog('反馈请求异常: ' + error.message);
                } finally {
                    btn.disabled = false;
                    btn.textContent = '发送反馈图像';
                }
            }
            
            // 重置任务
            async function resetTask() {
                try {
                    const response = await fetch('/web/reset', {
                        method: 'POST'
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        document.getElementById('aiResponse').textContent = '任务已重置';
                        document.getElementById('taskQuery').value = '';
                        addLog('任务已重置');
                    } else {
                        addLog('任务重置失败: ' + data.error);
                    }
                } catch (error) {
                    addLog('重置请求异常: ' + error.message);
                }
            }
            
            // 紧急停止
            async function emergencyStop() {
                try {
                    const response = await fetch('/web/emergency-stop', {
                        method: 'POST'
                    });
                    
                    const data = await response.json();
                    addLog('紧急停止: ' + (data.success ? '成功' : '失败 - ' + data.error));
                } catch (error) {
                    addLog('紧急停止请求异常: ' + error.message);
                }
            }
            
            // 添加日志
            function addLog(message) {
                const logDiv = document.getElementById('executionLog');
                const timestamp = new Date().toLocaleTimeString();
                logDiv.textContent += `[${timestamp}] ${message}\n`;
                logDiv.scrollTop = logDiv.scrollHeight;
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@router.post("/upload")
async def upload_image(file: UploadFile = File(...)):
    """上传图像文件"""
    try:
        # 验证文件类型
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只支持图像文件")
        
        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        file_extension = Path(file.filename).suffix.lower()
        if not file_extension:
            file_extension = '.jpg'
        
        file_path = upload_dir / f"{file_id}{file_extension}"
        
        # 保存文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 验证图像
        is_valid, error_msg = ImageProcessor.validate_image(file_path)
        if not is_valid:
            os.remove(file_path)
            raise HTTPException(status_code=400, detail=f"图像文件无效: {error_msg}")
        
        return JSONResponse({
            "success": True,
            "image_id": file_id,
            "filename": file.filename,
            "size": file_path.stat().st_size
        })
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@router.get("/image/{image_id}")
async def get_image(image_id: str):
    """获取上传的图像"""
    try:
        # 查找图像文件
        image_files = list(upload_dir.glob(f"{image_id}.*"))
        if not image_files:
            raise HTTPException(status_code=404, detail="图像不存在")
        
        image_path = image_files[0]
        
        # 返回图像文件
        from fastapi.responses import FileResponse
        return FileResponse(image_path)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图像失败: {str(e)}")

@router.post("/task")
async def start_task(request: TaskRequest, controller: AIRobotController = Depends(get_ai_controller)):
    """开始执行任务"""
    try:
        # 查找图像文件
        image_files = list(upload_dir.glob(f"{request.image_id}.*"))
        if not image_files:
            raise HTTPException(status_code=404, detail="图像不存在")
        
        image_path = str(image_files[0])
        
        # 处理任务
        result = await controller.process_task_with_image(request.query, image_path)
        
        if result.get('stage') == 'ERROR':
            return JSONResponse({
                "success": False,
                "error": result.get('error', '未知错误')
            })
        
        return JSONResponse({
            "success": True,
            "stage": result.get('stage'),
            "response": result.get('response'),
            "parsed_data": result.get('parsed_data'),
            "execution": result.get('execution')
        })
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"任务执行失败: {str(e)}")

@router.post("/feedback")
async def send_feedback(request: FeedbackRequest, controller: AIRobotController = Depends(get_ai_controller)):
    """发送反馈图像"""
    try:
        # 查找图像文件
        image_files = list(upload_dir.glob(f"{request.image_id}.*"))
        if not image_files:
            raise HTTPException(status_code=404, detail="图像不存在")
        
        image_path = str(image_files[0])
        
        # 处理反馈
        result = await controller.process_feedback_image(image_path)
        
        if result.get('stage') == 'ERROR':
            return JSONResponse({
                "success": False,
                "error": result.get('error', '未知错误')
            })
        
        return JSONResponse({
            "success": True,
            "stage": result.get('stage'),
            "response": result.get('response'),
            "parsed_data": result.get('parsed_data')
        })
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"反馈处理失败: {str(e)}")

@router.get("/status")
async def get_system_status(controller: AIRobotController = Depends(get_ai_controller)):
    """获取系统状态"""
    try:
        # 测试AI连接
        ai_connected, ai_message = controller.doubao_client.test_connection()
        
        # 获取机械臂状态
        robot_connected = robot_client is not None
        robot_running = robot_client.running if robot_client else False
        
        # 获取控制器状态
        controller_status = controller.get_current_status()
        
        return JSONResponse({
            "success": True,
            "status": {
                "ai_connected": ai_connected,
                "ai_message": ai_message,
                "robot_connected": robot_connected,
                "robot_running": robot_running,
                "current_stage": controller_status.get('current_stage'),
                "is_executing": controller_status.get('is_executing', False)
            }
        })
        
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        })

@router.post("/reset")
async def reset_system(controller: AIRobotController = Depends(get_ai_controller)):
    """重置系统状态"""
    try:
        controller.reset_task()
        
        return JSONResponse({
            "success": True,
            "message": "系统已重置"
        })
        
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        })

@router.post("/emergency-stop")
async def emergency_stop(controller: AIRobotController = Depends(get_ai_controller)):
    """紧急停止"""
    try:
        success = await controller.stop_all_commands()
        
        return JSONResponse({
            "success": success,
            "message": "紧急停止" + ("成功" if success else "失败")
        })
        
    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": str(e)
        })
