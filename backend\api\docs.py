from fastapi import APIRouter, HTTPException, Body, Path, Query, Depends
from pydantic import BaseModel
from typing import List, Dict, Optional, Any
import os
import glob
import re
import json
from datetime import datetime

# 创建路由
router = APIRouter()

# 文档根目录
DOCS_DIR = os.environ.get("DOCS_DIR", "./docs")

# 确保文档目录存在
os.makedirs(DOCS_DIR, exist_ok=True)

# 初始化README文件
README_FILE = os.path.join(DOCS_DIR, "index.md")
if not os.path.exists(README_FILE):
    with open(README_FILE, "w", encoding="utf-8") as f:
        f.write("# 智能体控制平台\n\n欢迎使用智能体控制平台文档。")

# 文档内容模型
class DocumentContent(BaseModel):
    content: str

# 搜索查询模型
class SearchQuery(BaseModel):
    query: str

# 获取文档列表
@router.get("/list")
async def list_docs():
    """
    获取所有文档列表
    """
    try:
        docs = []
        for file_path in glob.glob(os.path.join(DOCS_DIR, "**/*.md"), recursive=True):
            rel_path = os.path.relpath(file_path, DOCS_DIR)
            docs.append({
                "path": rel_path,
                "title": get_doc_title(file_path),
                "modified": os.path.getmtime(file_path)
            })
        
        # 按修改时间排序（最新的在前）
        docs.sort(key=lambda x: x["modified"], reverse=True)
        
        return docs
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文档列表失败: {str(e)}")

# 获取文档内容
@router.get("/content/{doc_path:path}")
async def get_doc_content(doc_path: str):
    """
    获取指定文档的内容
    """
    try:
        # 构建文件路径
        if doc_path == "index" or not doc_path:
            doc_path = "index.md"
        elif not doc_path.endswith(".md"):
            doc_path = f"{doc_path}.md"
        
        file_path = os.path.join(DOCS_DIR, doc_path)
        file_path = os.path.normpath(file_path)
        
        # 确保路径在文档目录内
        if not file_path.startswith(DOCS_DIR):
            raise HTTPException(status_code=403, detail="禁止访问文档目录之外的文件")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail=f"文档不存在: {doc_path}")
        
        # 读取文件内容
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        return {
            "path": doc_path,
            "title": get_doc_title(file_path),
            "content": content,
            "modified": os.path.getmtime(file_path)
        }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"获取文档内容失败: {str(e)}")

# 更新文档内容
@router.put("/content/{doc_path:path}")
async def update_doc_content(doc_path: str, document: DocumentContent):
    """
    更新或创建文档内容
    """
    try:
        # 构建文件路径
        if not doc_path.endswith(".md"):
            doc_path = f"{doc_path}.md"
        
        file_path = os.path.join(DOCS_DIR, doc_path)
        file_path = os.path.normpath(file_path)
        
        # 确保路径在文档目录内
        if not file_path.startswith(DOCS_DIR):
            raise HTTPException(status_code=403, detail="禁止访问文档目录之外的文件")
        
        # 确保父目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        # 写入文件内容
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(document.content)
        
        return {
            "status": "success",
            "message": "文档内容已更新",
            "path": doc_path
        }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"更新文档内容失败: {str(e)}")

# 删除文档
@router.delete("/content/{doc_path:path}")
async def delete_doc(doc_path: str):
    """
    删除指定的文档
    """
    try:
        # 构建文件路径
        if not doc_path.endswith(".md"):
            doc_path = f"{doc_path}.md"
        
        file_path = os.path.join(DOCS_DIR, doc_path)
        file_path = os.path.normpath(file_path)
        
        # 确保路径在文档目录内
        if not file_path.startswith(DOCS_DIR):
            raise HTTPException(status_code=403, detail="禁止访问文档目录之外的文件")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail=f"文档不存在: {doc_path}")
        
        # 不允许删除主文档
        if os.path.normpath(file_path) == os.path.normpath(README_FILE):
            raise HTTPException(status_code=400, detail="不能删除主文档")
        
        # 删除文件
        os.remove(file_path)
        
        return {
            "status": "success",
            "message": f"文档已删除: {doc_path}"
        }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"删除文档失败: {str(e)}")

# 搜索文档
@router.post("/search")
async def search_docs(query: SearchQuery):
    """
    在文档中搜索内容
    """
    try:
        search_results = []
        
        # 遍历所有文档文件
        for file_path in glob.glob(os.path.join(DOCS_DIR, "**/*.md"), recursive=True):
            rel_path = os.path.relpath(file_path, DOCS_DIR)
            
            # 读取文件内容
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # 检查查询是否存在于内容中
            if query.query.lower() in content.lower():
                # 获取匹配内容的上下文
                context = get_search_context(content, query.query)
                
                search_results.append({
                    "path": rel_path,
                    "title": get_doc_title(file_path),
                    "context": context
                })
        
        return search_results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索文档失败: {str(e)}")

# 辅助函数：从文档内容中提取标题
def get_doc_title(file_path):
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 查找第一个Markdown标题
        title_match = re.search(r'^#\s+(.+)$', content, re.MULTILINE)
        if title_match:
            return title_match.group(1)
        
        # 如果没有找到标题，返回文件名
        return os.path.splitext(os.path.basename(file_path))[0]
    except Exception:
        return os.path.splitext(os.path.basename(file_path))[0]

# 辅助函数：获取搜索匹配的上下文
def get_search_context(content, query, context_length=100):
    query_lower = query.lower()
    content_lower = content.lower()
    
    # 找到查询在内容中的位置
    pos = content_lower.find(query_lower)
    if pos == -1:
        return ""
    
    # 计算上下文范围
    start = max(0, pos - context_length // 2)
    end = min(len(content), pos + len(query) + context_length // 2)
    
    # 调整起始位置到单词边界
    if start > 0:
        while start > 0 and content[start].isalnum():
            start -= 1
    
    # 调整结束位置到单词边界
    if end < len(content):
        while end < len(content) and content[end].isalnum():
            end += 1
    
    # 提取上下文
    context = content[start:end]
    
    # 添加省略号表示截断
    if start > 0:
        context = "..." + context
    if end < len(content):
        context = context + "..."
    
    return context 