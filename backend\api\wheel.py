from fastapi import APIRouter, HTTPException, WebSocket, Body, WebSocketDisconnect
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import asyncio
import json
import time
import threading
import zmq

# 创建路由
router = APIRouter()

# 轮子控制配置数据模型
class WheelControlConfig(BaseModel):
    ip: str
    port: int = 5555
    video_port: int = 5556
    speed: int = 1000
    speed_step: int = 500

# 轮子速度命令模型
class WheelVelocityCommand(BaseModel):
    left_wheel: int = 0
    back_wheel: int = 0
    right_wheel: int = 0

# 方向命令模型
class DirectionCommand(BaseModel):
    direction: str  # forward, backward, left, right, stop
    action: str = "press"  # press 或 release

# 全局连接管理器
class WheelConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        
    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            
    async def broadcast(self, message: dict):
        for connection in self.active_connections:
            try:
                await connection.send_json(message)
            except Exception as e:
                print(f"广播错误: {e}")

# 全局变量
wheel_manager = WheelConnectionManager()
controller = None
controller_thread = None
is_controller_running = False
latest_frame = None
latest_frame_lock = threading.Lock()

# 初始化函数 - 确保latest_frame正确初始化
def init_wheel_module():
    """初始化轮子模块，确保所有全局变量正确设置"""
    global latest_frame, latest_frame_lock
    
    # 初始化一个空的latest_frame
    if latest_frame is None:
        with latest_frame_lock:
            latest_frame = {
                "camera": "default",
                "image": "",
                "timestamp": time.time()
            }
    
    print("轮子模块初始化完成")

# 确保模块加载时初始化
init_wheel_module()

# 轮子控制器类
class WheelController:
    def __init__(self, config: WheelControlConfig):
        self.config = config
        self.speed = config.speed
        self.running = False
        self.video_socket = None
        self.cmd_socket = None
        self.context = None
        
        try:
            # 初始化ZMQ
            print(f"初始化ZMQ连接: IP={self.config.ip}, 命令端口={self.config.port}, 视频端口={self.config.video_port}")
            self.context = zmq.Context()
            
            # 设置命令socket
            self.cmd_socket = self.context.socket(zmq.PUSH)
            self.cmd_socket.setsockopt(zmq.CONFLATE, 1)
            self.cmd_socket.connect(f"tcp://{self.config.ip}:{self.config.port}")
            print(f"命令socket连接成功")
            
            # 视频反馈socket
            self.video_socket = self.context.socket(zmq.PULL)
            self.video_socket.setsockopt(zmq.CONFLATE, 1)
            self.video_socket.connect(f"tcp://{self.config.ip}:{self.config.video_port}")
            print(f"视频socket连接成功")
            
            # 存储最新的速度数据
            self.current_speed = {"left_wheel": 0, "back_wheel": 0, "right_wheel": 0}
        except Exception as e:
            print(f"初始化ZMQ连接失败: {e}")
            self.disconnect()
            raise

    def stop_all_wheels(self):
        """停止所有轮子"""
        wheel_commands = {
            "raw_velocity": {
                "left_wheel": 0,
                "back_wheel": 0,
                "right_wheel": 0
            }
        }
        self.cmd_socket.send_string(json.dumps(wheel_commands))
        return "所有轮子已停止"

    def move_forward(self):
        """向前移动"""
        wheel_commands = {
            "raw_velocity": {
                "left_wheel": self.speed,
                "back_wheel": 0,
                "right_wheel": self.speed | 0x8000
            }
        }
        self.cmd_socket.send_string(json.dumps(wheel_commands))
        return "前进"

    def move_backward(self):
        """向后移动"""
        wheel_commands = {
            "raw_velocity": {
                "left_wheel": self.speed | 0x8000,
                "back_wheel": 0,
                "right_wheel": self.speed
            }
        }
        self.cmd_socket.send_string(json.dumps(wheel_commands))
        return "后退"

    def rotate_left(self):
        """左转"""
        wheel_commands = {
            "raw_velocity": {
                "left_wheel": self.speed,
                "back_wheel": self.speed,
                "right_wheel": self.speed
            }
        }
        self.cmd_socket.send_string(json.dumps(wheel_commands))
        return "左转"

    def rotate_right(self):
        """右转"""
        wheel_commands = {
            "raw_velocity": {
                "left_wheel": self.speed | 0x8000,
                "back_wheel": self.speed | 0x8000,
                "right_wheel": self.speed | 0x8000
            }
        }
        self.cmd_socket.send_string(json.dumps(wheel_commands))
        return "右转"

    def set_raw_velocity(self, command: WheelVelocityCommand):
        """设置原始速度值"""
        wheel_commands = {
            "raw_velocity": {
                "left_wheel": command.left_wheel,
                "back_wheel": command.back_wheel,
                "right_wheel": command.right_wheel
            }
        }
        self.cmd_socket.send_string(json.dumps(wheel_commands))
        return f"设置速度: {command.dict()}"

    def increase_speed(self):
        """增加速度"""
        self.speed += self.config.speed_step
        return f"速度增加到 {self.speed}"

    def decrease_speed(self):
        """减少速度"""
        self.speed = max(0, self.speed - self.config.speed_step)
        return f"速度减少到 {self.speed}"

    def video_thread(self):
        """接收并广播视频流"""
        global latest_frame, latest_frame_lock
        
        print(f"视频线程启动")
        
        while self.running:
            try:
                if not self.video_socket:
                    print("视频socket未初始化或已关闭，重新连接中...")
                    time.sleep(1)
                    continue
                
                # 轮询视频socket 100ms
                poller = zmq.Poller()
                poller.register(self.video_socket, zmq.POLLIN)
                socks = dict(poller.poll(100))
                
                if self.video_socket in socks and socks[self.video_socket] == zmq.POLLIN:
                    # 接收最新的观测数据
                    obs_string = self.video_socket.recv_string(zmq.NOBLOCK)
                    try:
                        observation = json.loads(obs_string)
                        
                        # 处理图像数据
                        images_dict = observation.get("images", {})
                        for cam_name, image_b64 in images_dict.items():
                            if image_b64:
                                # 存储最新图像
                                with latest_frame_lock:
                                    latest_frame = {
                                        "camera": cam_name,
                                        "image": image_b64,
                                        "timestamp": time.time()
                                    }
                                # print(f"接收到新的视频帧: camera={cam_name}, 大小={len(image_b64)} bytes")
                    
                        # 存储速度数据
                        speed_data = observation.get("present_speed", {})
                        if speed_data:
                            self.current_speed = speed_data
                    except json.JSONDecodeError as e:
                        print(f"解析JSON数据失败: {e}")
                
            except zmq.ZMQError as e:
                print(f"ZMQ错误: {e}")
                # 如果是"not a socket"错误，尝试重新初始化socket
                if "not a socket" in str(e):
                    try:
                        print("尝试重新初始化视频socket...")
                        self.video_socket = self.context.socket(zmq.PULL)
                        self.video_socket.setsockopt(zmq.CONFLATE, 1)
                        self.video_socket.connect(f"tcp://{self.config.ip}:{self.config.video_port}")
                        print("视频socket重新初始化成功")
                    except Exception as e2:
                        print(f"重新初始化视频socket失败: {e2}")
                        time.sleep(2)  # 等待一段时间再重试
            except Exception as e:
                print(f"视频线程错误: {e}")
                time.sleep(1)  # 避免错误情况下的快速循环
            
            time.sleep(0.01)
        
        print("视频线程结束")
    
    def start(self):
        """启动控制器"""
        print("开始启动控制器...")
        self.running = True
        self.stop_all_wheels()
        
        # 启动视频线程
        video_thread = threading.Thread(target=self.video_thread, daemon=True)
        video_thread.start()
        print("视频线程已启动")
        
        return "控制器已启动"
    
    def stop(self):
        """停止控制器"""
        print("停止控制器...")
        self.running = False
        self.stop_all_wheels()
        
        return "控制器已停止"
    
    def disconnect(self):
        """断开连接"""
        print("断开ZMQ连接...")
        try:
            if self.cmd_socket:
                self.cmd_socket.close()
                self.cmd_socket = None
                print("命令socket已关闭")
            if self.video_socket:
                self.video_socket.close()
                self.video_socket = None
                print("视频socket已关闭")
            if self.context:
                self.context.term()
                self.context = None
                print("ZMQ上下文已终止")
        except Exception as e:
            print(f"断开连接时发生错误: {e}")
        
        return "已断开连接"

# 启动控制器线程
def run_controller(config: WheelControlConfig):
    global controller, is_controller_running
    
    controller = WheelController(config)
    controller.start()
    is_controller_running = True
    
    # 持续运行直到停止标志
    while is_controller_running and controller.running:
        time.sleep(0.1)
    
    controller.stop()
    controller.disconnect()
    controller = None
    is_controller_running = False

# API路由定义
@router.post("/connect")
async def connect_to_wheel(config: WheelControlConfig):
    """连接到远程万向轮设备"""
    global controller, controller_thread, is_controller_running
    
    try:
        # 检查是否已有连接
        if is_controller_running and controller:
            return {
                "status": "success",
                "message": "已经连接到设备"
            }
        
        # 启动新的控制器线程
        is_controller_running = True
        controller_thread = threading.Thread(
            target=run_controller,
            args=(config,),
            daemon=True
        )
        controller_thread.start()
        
        return {
            "status": "success",
            "message": f"已连接到设备 {config.ip}",
            "config": config.dict()
        }
    except Exception as e:
        is_controller_running = False
        raise HTTPException(status_code=500, detail=f"连接失败: {str(e)}")

@router.post("/disconnect")
async def disconnect_from_wheel():
    """断开与远程万向轮设备的连接"""
    global controller, is_controller_running
    
    try:
        if not is_controller_running or not controller:
            return {
                "status": "warning",
                "message": "当前没有连接"
            }
        
        # 停止控制器
        is_controller_running = False
        message = "已断开连接"
        
        return {
            "status": "success",
            "message": message
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"断开连接失败: {str(e)}")

@router.post("/direction")
async def control_direction(command: DirectionCommand):
    """控制方向"""
    global controller
    
    try:
        if not controller or not is_controller_running:
            raise HTTPException(status_code=400, detail="未连接到设备")
        
        result = "未知命令"
        
        # 处理方向命令
        if command.action == "press":
            if command.direction == "forward":
                result = controller.move_forward()
            elif command.direction == "backward":
                result = controller.move_backward()
            elif command.direction == "left":
                result = controller.rotate_left()
            elif command.direction == "right":
                result = controller.rotate_right()
            elif command.direction == "stop":
                result = controller.stop_all_wheels()
        elif command.action == "release":
            # 松开按钮时停止
            result = controller.stop_all_wheels()
        
        return {
            "status": "success",
            "message": result,
            "command": command.dict()
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"控制失败: {str(e)}")

@router.post("/speed")
async def set_speed(speed: int):
    """设置速度"""
    global controller
    
    try:
        if not controller or not is_controller_running:
            raise HTTPException(status_code=400, detail="未连接到设备")
        
        controller.speed = speed
        
        return {
            "status": "success",
            "message": f"速度已设置为 {speed}",
            "speed": speed
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"设置速度失败: {str(e)}")

@router.post("/speed/increase")
async def increase_speed():
    """增加速度"""
    global controller
    
    try:
        if not controller or not is_controller_running:
            raise HTTPException(status_code=400, detail="未连接到设备")
        
        result = controller.increase_speed()
        
        return {
            "status": "success",
            "message": result,
            "speed": controller.speed
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"增加速度失败: {str(e)}")

@router.post("/speed/decrease")
async def decrease_speed():
    """减少速度"""
    global controller
    
    try:
        if not controller or not is_controller_running:
            raise HTTPException(status_code=400, detail="未连接到设备")
        
        result = controller.decrease_speed()
        
        return {
            "status": "success",
            "message": result,
            "speed": controller.speed
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"减少速度失败: {str(e)}")

@router.post("/raw")
async def set_raw_velocity(command: WheelVelocityCommand):
    """设置原始速度值"""
    global controller
    
    try:
        if not controller or not is_controller_running:
            raise HTTPException(status_code=400, detail="未连接到设备")
        
        result = controller.set_raw_velocity(command)
        
        return {
            "status": "success",
            "message": result,
            "command": command.dict()
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"设置速度失败: {str(e)}")

@router.get("/status")
async def get_status():
    """获取当前状态"""
    global controller, is_controller_running
    
    status_data = {
        "connected": is_controller_running and controller is not None,
        "speed": controller.speed if controller else 0,
        "current_speed": controller.current_speed if controller else {"left_wheel": 0, "back_wheel": 0, "right_wheel": 0}
    }
    
    return status_data

# 添加一个简化的WebSocket路由，专门用于视频流
@router.websocket("/simple_video")
async def simple_video_endpoint(websocket: WebSocket):
    """简化的视频WebSocket端点，仅发送视频帧"""
    global latest_frame, latest_frame_lock
    
    client_host = websocket.client.host if hasattr(websocket, 'client') else "未知"
    print(f"接收到简化视频WebSocket连接请求 [IP: {client_host}, ID: {id(websocket)}]")
    
    try:
        # 接受连接，无任何限制
        await websocket.accept()
        print(f"简化视频WebSocket连接已建立 [IP: {client_host}, ID: {id(websocket)}]")
        
        # 发送连接确认
        await websocket.send_json({"status": "connected", "message": "WebSocket连接已成功建立"})
        print(f"已发送连接确认消息 [ID: {id(websocket)}]")
        
        # 持续发送视频帧
        while True:
            # 获取当前图像
            current_frame = None
            with latest_frame_lock:
                if latest_frame and "image" in latest_frame:
                    current_frame = latest_frame["image"]
                    if current_frame:
                        print(f"获取到图像数据，大小: {len(current_frame)} bytes")
                else:
                    print(f"等待图像数据... [latest_frame存在: {latest_frame is not None}]")
            
            # 如果有图像，就发送
            if current_frame:
                try:
                    await websocket.send_json({"image": current_frame})
                    print(f"已发送图像帧到客户端 [ID: {id(websocket)}]")
                except Exception as e:
                    print(f"发送视频帧错误: {e} [ID: {id(websocket)}]")
                    break
            else:
                # 发送空状态消息，保持连接活跃
                try:
                    await websocket.send_json({"status": "waiting", "message": "等待图像数据..."})
                except Exception as e:
                    print(f"发送状态消息错误: {e} [ID: {id(websocket)}]")
                    break
            
            # 控制帧率
            await asyncio.sleep(0.1)  # 10 FPS
            
    except WebSocketDisconnect:
        print(f"简化视频WebSocket客户端断开连接 [ID: {id(websocket)}]")
    except ConnectionRefusedError as e:
        print(f"连接被拒绝: {e} [ID: {id(websocket)}]")
    except Exception as e:
        print(f"简化视频WebSocket错误: {str(e)} [ID: {id(websocket)}]")
    finally:
        # 确保连接关闭
        try:
            await websocket.close()
        except:
            pass
        print(f"WebSocket连接已关闭 [ID: {id(websocket)}]") 