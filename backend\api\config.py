from fastapi import APIRouter, HTTPException, Body, Path, Query
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any
import os
import json
import uuid
from datetime import datetime

# 创建路由
router = APIRouter()

# 定义模型
class AgentPrompt(BaseModel):
    system: str = Field("你是一台智能机械臂。", description="系统提示词")
    user: str = Field("请回答我的问题并完成我提出的任务。", description="用户提示词")

class ApiSettings(BaseModel):
    url: str = Field("https://api.example.com", description="API地址")
    api_key: Optional[str] = Field(None, description="API密钥")
    model: str = Field("gpt-3.5-turbo", description="模型名称")
    temperature: float = Field(0.7, description="温度参数", ge=0.0, le=1.0)
    max_tokens: int = Field(2048, description="最大生成token数", ge=1)

class Agent(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    created_at: str
    updated_at: str
    prompts: AgentPrompt
    api_settings: ApiSettings
    is_active: bool = False

class OrangePiConfig(BaseModel):
    address: str

# 配置文件路径
CONFIG_DIR = os.environ.get("CONFIG_DIR", "./configs")
AGENTS_FILE = os.path.join(CONFIG_DIR, "agents.json")

# 确保配置目录存在
os.makedirs(CONFIG_DIR, exist_ok=True)

# 初始化一个默认智能体
default_agent = Agent(
    id="default",
    name="默认智能体",
    description="系统默认的智能体配置",
    created_at=datetime.now().isoformat(),
    updated_at=datetime.now().isoformat(),
    prompts=AgentPrompt(),
    api_settings=ApiSettings(),
    is_active=True
)

# 活跃智能体ID
active_agent_id = "default"

# 全局变量，用于存储配置
CONFIG = {
    "ORANGEPI_SERVICE_URL": os.environ.get("ORANGEPI_SERVICE_URL", "http://************:8001")
}

# 加载智能体配置
def load_agents():
    if not os.path.exists(AGENTS_FILE):
        # 如果文件不存在，创建一个默认配置
        agents = [default_agent.dict()]
        with open(AGENTS_FILE, "w", encoding="utf-8") as f:
            json.dump(agents, f, ensure_ascii=False, indent=2)
        return agents
    
    with open(AGENTS_FILE, "r", encoding="utf-8") as f:
        return json.load(f)

# 保存智能体配置
def save_agents(agents):
    with open(AGENTS_FILE, "w", encoding="utf-8") as f:
        json.dump(agents, f, ensure_ascii=False, indent=2)

# 获取所有智能体
@router.get("/agents")
async def get_agents():
    """
    获取所有配置的智能体列表
    """
    global active_agent_id
    agents = load_agents()
    
    # 更新活跃状态
    for agent in agents:
        agent["is_active"] = (agent["id"] == active_agent_id)
    
    return agents

# 获取单个智能体
@router.get("/agents/{agent_id}")
async def get_agent(agent_id: str):
    """
    获取指定ID的智能体配置
    """
    agents = load_agents()
    for agent in agents:
        if agent["id"] == agent_id:
            return agent
    
    raise HTTPException(status_code=404, detail=f"智能体不存在: {agent_id}")

# 创建新智能体
@router.post("/agents")
async def create_agent(agent: Agent):
    """
    创建新的智能体配置
    """
    agents = load_agents()
    
    # 生成唯一ID并设置时间戳
    agent.id = str(uuid.uuid4())
    agent.created_at = datetime.now().isoformat()
    agent.updated_at = datetime.now().isoformat()
    
    # 添加新智能体
    agents.append(agent.dict())
    save_agents(agents)
    
    return agent

# 更新智能体
@router.put("/agents/{agent_id}")
async def update_agent(agent_id: str, agent_data: dict = Body(...)):
    """
    更新指定ID的智能体配置
    """
    agents = load_agents()
    
    # 查找并更新智能体
    for i, agent in enumerate(agents):
        if agent["id"] == agent_id:
            # 保留ID和创建时间
            agent_data["id"] = agent_id
            agent_data["created_at"] = agent["created_at"]
            agent_data["updated_at"] = datetime.now().isoformat()
            
            # 更新智能体
            agents[i] = agent_data
            save_agents(agents)
            
            return agent_data
    
    raise HTTPException(status_code=404, detail=f"智能体不存在: {agent_id}")

# 删除智能体
@router.delete("/agents/{agent_id}")
async def delete_agent(agent_id: str):
    """
    删除指定ID的智能体配置
    """
    global active_agent_id
    
    # 不允许删除默认智能体
    if agent_id == "default":
        raise HTTPException(status_code=400, detail="不能删除默认智能体")
    
    agents = load_agents()
    
    # 查找并删除智能体
    for i, agent in enumerate(agents):
        if agent["id"] == agent_id:
            if agent_id == active_agent_id:
                # 如果删除的是当前活跃智能体，切换回默认智能体
                active_agent_id = "default"
            
            # 删除智能体
            del agents[i]
            save_agents(agents)
            
            return {"status": "success", "message": f"智能体已删除: {agent_id}"}
    
    raise HTTPException(status_code=404, detail=f"智能体不存在: {agent_id}")

# 选择活跃智能体
@router.post("/select/{agent_id}")
async def select_agent(agent_id: str):
    """
    选择当前使用的智能体
    """
    global active_agent_id
    
    agents = load_agents()
    
    # 查找智能体
    for agent in agents:
        if agent["id"] == agent_id:
            active_agent_id = agent_id
            return {"status": "success", "message": f"已选择智能体: {agent_id}"}
    
    raise HTTPException(status_code=404, detail=f"智能体不存在: {agent_id}")

# 导出配置
@router.get("/export")
async def export_config():
    """
    导出所有配置
    """
    agents = load_agents()
    return agents

# 导入配置
@router.post("/import")
async def import_config(configs: List[dict] = Body(...)):
    """
    导入配置
    """
    try:
        # 验证配置
        for config in configs:
            if "id" not in config or "name" not in config:
                raise HTTPException(status_code=400, detail="无效的配置格式")
        
        # 保存配置
        save_agents(configs)
        
        return {"status": "success", "message": "配置导入成功"}
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"配置导入失败: {str(e)}")

@router.post("/set_orangepi_address")
async def set_orangepi_address(config: OrangePiConfig):
    """设置香橙派服务地址"""
    try:
        # 更新全局配置
        CONFIG["ORANGEPI_SERVICE_URL"] = config.address
        # 设置环境变量，让其他模块可以访问
        os.environ["ORANGEPI_SERVICE_URL"] = config.address
        
        # 同时设置ARM_SERVICE_URL环境变量，添加/api/arm路径
        arm_service_url = config.address
        if not arm_service_url.endswith("/api/arm"):
            if arm_service_url.endswith("/"):
                arm_service_url += "api/arm"
            else:
                arm_service_url += "/api/arm"
        os.environ["ARM_SERVICE_URL"] = arm_service_url
        
        return {
            "status": "success",
            "message": f"香橙派服务地址已设置为: {config.address}，机械臂服务地址: {arm_service_url}"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"设置香橙派地址失败: {str(e)}")

@router.get("/get_orangepi_address")
async def get_orangepi_address():
    """获取当前香橙派服务地址"""
    return {
        "status": "success",
        "address": CONFIG["ORANGEPI_SERVICE_URL"]
    } 