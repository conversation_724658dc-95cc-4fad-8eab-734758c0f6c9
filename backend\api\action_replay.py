#!/usr/bin/env python
# 动作重播模块：实现主臂从臂连接、动作录制和动作重播

import os
import time
import json
import asyncio
import threading
from datetime import datetime
from fastapi import APIRouter, HTTPException, Body
from pydantic import BaseModel
from typing import Dict, Any, Optional, List, Union
from lerobot.common.robot_devices.robots.utils import make_robot_from_config
from lerobot.common.robot_devices.control_utils import sanity_check_dataset_name,record_episode
from lerobot.common.robot_devices.robots.configs import So101RobotConfig
from lerobot.common.robot_devices.robots.configs import So100RobotConfig
from lerobot.common.datasets.lerobot_dataset import LeRobotDataset
from lerobot.common.robot_devices.control_utils import log_control_info
from lerobot.common.utils.utils import init_logging
import numpy as np



# 创建路由
router = APIRouter(tags=["action_replay"])

# 定义模型
class RobotConnectionRequest(BaseModel):
    """用于连接主臂和从臂的请求模型"""
    main_port: str = "/dev/ttyACM1"  # 主臂端口
    follower_port: str = "/dev/ttyACM0"  # 从臂端口
    robot_type: str = "so100"  # 机械臂类型，默认为 SO100

class RecordControlConfig:
    """录制控制配置类"""
    def __init__(self, 
                 repo_id: str,
                 root: str = None,
                 fps: int = 30,
                 warmup_time_s: float = 1.0,
                 episode_time_s: float = 10.0,
                 reset_time_s: float = 1.0,
                 display_data: bool = False,
                 play_sounds: bool = False,
                 num_episodes: int = 1,
                 policy: str = None,
                 resume: bool = False,
                 single_task: Union[bool, str] = True,
                 push_to_hub: bool = False,  # 设置为False，避免尝试访问HuggingFace
                 private: bool = False,
                 tags: list = None,
                 num_image_writer_processes: int = 0,
                 num_image_writer_threads_per_camera: int = 0,
                 video: bool = False):  # 设置为False，避免尝试创建视频
        self.repo_id = repo_id  # 数据存储目录名称
        self.root = root or os.path.expanduser("~/robot_action_recordings")
        self.fps = fps  # 录制帧率
        self.warmup_time_s = warmup_time_s  # 预热时间（秒）
        self.episode_time_s = episode_time_s  # 每个片段的时长（秒）
        self.reset_time_s = reset_time_s  # 重置时间（秒）
        self.display_data = display_data  # 是否显示数据
        self.play_sounds = play_sounds  # 是否播放声音提示
        self.num_episodes = num_episodes  # 录制片段数量
        self.policy = policy  # 控制策略
        self.resume = resume  # 是否继续之前的录制
        # 单一任务描述，如果为True，使用默认描述字符串
        self.single_task = "Perform the robot action" if single_task is True else single_task  
        self.push_to_hub = push_to_hub  # 是否上传到HuggingFace
        self.private = private  # 是否为私有数据集
        self.tags = tags or []  # 数据集标签
        self.num_image_writer_processes = num_image_writer_processes  # 图像写入进程数
        self.num_image_writer_threads_per_camera = num_image_writer_threads_per_camera  # 每个相机的图像写入线程数
        self.video = video  # 是否编码为视频

class ReplayControlConfig:
    """重播控制配置类"""
    def __init__(self,
                 repo_id: str,
                 root: str = None,
                 fps: int = 30,
                 episode: int = 0,
                 play_sounds: bool = False):
        self.repo_id = repo_id  # 数据存储目录名称
        self.root = root  # 如果root为None，LeRobotDataset会使用默认缓存路径
        self.fps = fps  # 重播帧率
        self.episode = episode  # 要重播的片段索引
        self.play_sounds = play_sounds  # 是否播放声音提示

class ActionRecordRequest(BaseModel):
    """动作录制请求模型"""
    record_name: str  # 录制动作的名称
    duration: Optional[int] = 10  # 录制持续时间，默认为10秒
    fps: Optional[int] = 30  # 录制帧率，默认为30fps
    display_data: Optional[bool] = False  # 是否显示数据
    warmup_time: Optional[float] = 1.0  # 预热时间（秒）
    reset_time: Optional[float] = 1.0  # 重置时间（秒）

class ActionPlayRequest(BaseModel):
    """动作播放请求模型"""
    record_name: str  # 要播放的录制动作名称
    fps: Optional[int] = 30  # 重播帧率，默认为30fps
    skip_frames: Optional[bool] = True  # 是否跳过偶数帧，默认为True

# 全局变量
connected_robot_instance = None  # 单一机器人实例
action_recordings = {}  # 存储录制的动作数据

# 动作录制保存目录
RECORDINGS_DIR = os.path.expanduser("~/robot_action_recordings")
os.makedirs(RECORDINGS_DIR, exist_ok=True)

# 辅助函数，检查方法是否存在
def has_method(obj, method_name):
    """检查对象是否具有指定方法"""
    return hasattr(obj, method_name) and callable(getattr(obj, method_name))

# 辅助函数，输出日志并可选播放声音
def log_say(message, play_sounds=False, blocking=False):
    """输出日志并可选播放声音"""
    print(f"[动作录制] {message}")
    # 实际项目中可以添加声音播放逻辑

# 辅助函数，检查数据集与机器人兼容性
def sanity_check_dataset_robot_compatibility(dataset, robot, fps, use_videos=False):
    """检查数据集与机器人是否兼容"""
    # 这里只是简单检查，实际项目中可能需要更复杂的检查
    if not has_method(robot, "get_state"):
        raise ValueError("机器人对象必须具有get_state方法")
    return True

# 初始化机械臂（主臂和从臂）
def init_robots(main_port: str, follower_port: str, robot_type: str):
    """初始化机器人整体"""
    
    global connected_robot_instance # 使用单一机器人实例的全局变量
    
    robot_type_lower = robot_type.lower()
    
    try:
        init_logging() 
        
        print(f"为 {robot_type_lower.upper()} 创建配置，主臂端口: {main_port}，从臂端口: {follower_port}")
        
        robot_config: Optional[Union[So101RobotConfig, So100RobotConfig]] = None
        if robot_type_lower == "so101":
            robot_config = So101RobotConfig()
        elif robot_type_lower == "so100":
            robot_config = So100RobotConfig()
        else:
            return False, f"不支持的机械臂类型: {robot_type}。当前支持: so100, so101"
        
        if hasattr(robot_config, "leader_arms") and "main" in robot_config.leader_arms:
            if hasattr(robot_config.leader_arms["main"], "port"):
                 robot_config.leader_arms["main"].port = main_port
                 print(f"设置主臂端口为 {main_port}")
            else:
                print(f"警告: So10xRobotConfig.leader_arms['main'] ('{type(robot_config.leader_arms['main']).__name__}') 没有 'port' 属性")

        if hasattr(robot_config, "follower_arms") and "main" in robot_config.follower_arms:
            if hasattr(robot_config.follower_arms["main"], "port"):
                robot_config.follower_arms["main"].port = follower_port
                print(f"设置从臂端口为 {follower_port}")
            else:
                print(f"警告: So10xRobotConfig.follower_arms['main'] ('{type(robot_config.follower_arms['main']).__name__}') 没有 'port' 属性")
        
        print(f"正在连接机器人 {robot_type_lower.upper()}...")
        temp_robot = make_robot_from_config(robot_config)
        temp_robot.connect() # 连接整个机器人
        
        connected_robot_instance = temp_robot # 将整个机器人实例赋给全局变量
        
        # 确认信息
        if hasattr(connected_robot_instance, "leader_arms") and connected_robot_instance.leader_arms.get("main"):
            print("主导臂 ('main') 是已连接机器人的一部分。")
        if hasattr(connected_robot_instance, "follower_arms") and connected_robot_instance.follower_arms.get("main"):
            print("从臂 ('main') 是已连接机器人的一部分。")
            
        return True, f"成功连接机器人 {robot_type_lower.upper()}"
    except Exception as e:
        error_msg = f"初始化机器人时出错: {e}"
        print(f"详细错误: {repr(e)}")
        connected_robot_instance = None # 确保出错时全局实例为 None
        return False, error_msg

# API 端点
@router.post("/connect")
async def connect_robots_endpoint(conn_request: RobotConnectionRequest):
    """连接机器人接口"""
    global connected_robot_instance
    
    if connected_robot_instance is not None:
        try:
            if hasattr(connected_robot_instance, 'is_connected'):
                if isinstance(connected_robot_instance.is_connected, bool) and connected_robot_instance.is_connected:
                    print("机器人已连接，尝试断开旧连接...")
                    if hasattr(connected_robot_instance, 'disconnect') and callable(getattr(connected_robot_instance, 'disconnect')):
                        connected_robot_instance.disconnect()
            connected_robot_instance = None
            print("旧连接已断开或实例已被清除。")
        except Exception as e:
            print(f"断开旧连接时出错: {e}")
            connected_robot_instance = None
    
    success, message = init_robots(conn_request.main_port, conn_request.follower_port, conn_request.robot_type)
    if success:
        return {"status": "success", "message": message}
    else:
        connected_robot_instance = None # 确保失败时清除
        raise HTTPException(status_code=500, detail=message)

@router.post("/disconnect")
async def disconnect_robots_endpoint():
    """断开机器人连接接口"""
    global connected_robot_instance
    
    if connected_robot_instance is None:
        return {"status": "success", "message": "机器人已断开或未连接"}
    
    try:
        if hasattr(connected_robot_instance, 'is_connected'):
            if isinstance(connected_robot_instance.is_connected, bool) and connected_robot_instance.is_connected:
                print("正在断开机器人连接...")
                if hasattr(connected_robot_instance, 'disconnect') and callable(getattr(connected_robot_instance, 'disconnect')):
                    connected_robot_instance.disconnect()
                    print("机器人已成功断开连接")
                else:
                    print("机器人实例没有disconnect方法")
        
        connected_robot_instance = None
        return {"status": "success", "message": "机器人已断开连接"}
    except Exception as e:
        error_msg = f"断开机器人连接时出错: {e}"
        print(error_msg)
        connected_robot_instance = None # 无论如何都清除实例
        return {"status": "warning", "message": error_msg}

@router.post("/record")
async def record_action_endpoint(record_request: ActionRecordRequest):
    """录制动作接口"""
    global connected_robot_instance, action_recordings

    if connected_robot_instance is None:
        raise HTTPException(status_code=400, detail="机器人未连接，请先调用 /connect")
    
    # 检查机器人连接状态
    if hasattr(connected_robot_instance, 'is_connected'):
        if isinstance(connected_robot_instance.is_connected, bool) and not connected_robot_instance.is_connected:
            raise HTTPException(status_code=400, detail="机器人已断开连接")
    
    record_name = record_request.record_name
    duration = record_request.duration
    fps = record_request.fps
    display_data = record_request.display_data
    warmup_time = record_request.warmup_time
    reset_time = record_request.reset_time

    # 生成唯一的动作ID，避免冲突
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    action_id = f"{record_name}_{timestamp}"
    # 使用规范的repo_id格式: angle2001/+动作名
    repo_id = f"angle2001/{record_name}"
    
    # 创建RecordControlConfig配置
    cfg = RecordControlConfig(
        repo_id=repo_id,  # 使用angle2001/动作名作为repo_id
        fps=fps,
        warmup_time_s=warmup_time,
        episode_time_s=duration,
        reset_time_s=reset_time,
        display_data=display_data,
        play_sounds=False, 
        num_episodes=1,    
        policy=None,       
        resume=False,      
        push_to_hub=False,  # 确保不会尝试访问HuggingFace
        video=False,        # 不创建视频
        single_task=True   
    )

    try:
        # 使用精简版录制函数
        dataset = record_simplified(connected_robot_instance, cfg)
        if dataset is None:
            raise HTTPException(status_code=500, detail="录制失败，无法创建或访问数据集")
        
        # 获取LeRobotDataset默认缓存路径
        cache_path = os.path.expanduser("~/.cache/huggingface/lerobot")
        cache_path = cache_path.replace('/', os.path.sep)  # 兼容Windows路径格式
        
        # 适应正确的目录结构
        dataset_path = os.path.join(cache_path, "angle2001", record_name)
        print(f"录制数据集保存路径: {dataset_path}")
        
        # 获取帧数量
        frames_count = dataset.num_frames if hasattr(dataset, 'num_frames') else 0
        
        # 记录到内存中，使用原始的record_name作为键
        action_recordings[record_name] = {
            "path": dataset_path,
            "repo_id": repo_id,
            "timestamp": timestamp,
            "frames": frames_count
        }
        
        return {
            "status": "success", 
            "message": "动作录制完成", 
            "record_name": record_name,
            "repo_id": repo_id,
            "dataset_path": dataset_path,
            "duration": cfg.episode_time_s,
            "frames": frames_count
        }
    except Exception as e:
        error_msg = f"录制过程中出错: {e}"
        print(f"详细错误: {repr(e)}")
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/play")
async def play_action_endpoint(play_request: ActionPlayRequest):
    """播放录制动作接口"""
    global connected_robot_instance, action_recordings
    
    if connected_robot_instance is None:
        raise HTTPException(status_code=400, detail="机器人未连接，请先调用 /connect")
    
    # 检查机器人连接状态
    if hasattr(connected_robot_instance, 'is_connected'):
        if isinstance(connected_robot_instance.is_connected, bool) and not connected_robot_instance.is_connected:
            raise HTTPException(status_code=400, detail="机器人已断开连接")
    
    record_name = play_request.record_name
    fps = play_request.fps
    skip_frames = play_request.skip_frames
    
    # 确定repo_id，应与录制时一致
    repo_id = f"angle2001/{record_name}"
    print(f"尝试播放动作: {record_name}, repo_id: {repo_id}")
    
    try:
        # 检查数据集是否存在
        cache_path = os.path.expanduser("~/.cache/huggingface/lerobot")
        cache_path = cache_path.replace('/', os.path.sep)  # 兼容Windows路径格式
        dataset_dir = os.path.join(cache_path, "angle2001", record_name)
        
        if not os.path.exists(dataset_dir):
            print(f"找不到动作数据目录: {dataset_dir}")
            raise HTTPException(status_code=404, detail=f"找不到动作记录: {record_name}")
            
        print(f"找到动作目录: {dataset_dir}")
        
        # 使用ReplayControlConfig配置回放
        cfg = ReplayControlConfig(
            repo_id=repo_id,
            fps=fps,
            episode=0,  # 默认重放第0个片段
            play_sounds=False
        )
        
        # 使用replay函数重放动作
        print(f"开始播放动作 {record_name}...")
        success, message = replay(connected_robot_instance, cfg)
        
        if not success:
            print(f"播放失败: {message}")
            raise HTTPException(status_code=500, detail=message)
        
        print(f"动作 {record_name} 播放完成")
        return {
            "status": "success", 
            "message": message, 
            "record_name": record_name,
            "repo_id": repo_id
        }
    except Exception as e:
        error_msg = f"播放动作时出错: {e}"
        print(f"详细错误: {repr(e)}")
        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/list")
async def list_actions_endpoint():
    """列出所有可用的录制动作"""
    # 构建绝对路径 - 使用完整的Windows路径格式
    user_home = os.path.expanduser("~")
    # 使用原始Windows路径格式
    cache_path = os.path.join(user_home, ".cache", "huggingface", "lerobot", "angle2001")
    
    print(f"尝试读取动作目录 (绝对路径): {cache_path}")
    action_list = []
    
    try:
        # 如果目录不存在，创建它
        if not os.path.exists(cache_path):
            print(f"动作目录不存在，尝试创建: {cache_path}")
            os.makedirs(cache_path, exist_ok=True)
        
        # 强制重新扫描目录
        if os.path.exists(cache_path) and os.path.isdir(cache_path):
            dir_content = os.listdir(cache_path)
            print(f"目录内容 ({len(dir_content)}项): {dir_content}")
            
            # 添加每个子目录作为动作
            for item in dir_content:
                item_path = os.path.join(cache_path, item)
                if os.path.isdir(item_path):
                    print(f"添加动作: {item}")
                    # 简化的动作信息
                    action_list.append({
                        "name": item,
                        "repo_id": f"angle2001/{item}"
                    })
        else:
            print(f"无法访问目录或路径不是目录: {cache_path}")
            # 尝试检查父目录
            parent_dir = os.path.dirname(cache_path)
            if os.path.exists(parent_dir):
                print(f"父目录存在: {parent_dir}")
                print(f"父目录内容: {os.listdir(parent_dir)}")
            else:
                print(f"父目录不存在: {parent_dir}")
        
        # 即使列表为空也返回成功
        print(f"动作列表中有 {len(action_list)} 项")
        return {"status": "success", "actions": action_list}
    except Exception as e:
        print(f"列出动作时出错: {str(e)}")
        # 返回空列表但不报错
        return {"status": "success", "message": str(e), "actions": []}

@router.delete("/delete/{record_name}")
async def delete_action_endpoint(record_name: str):
    """删除指定的录制动作"""
    # 处理Windows系统路径
    cache_path = os.path.expanduser("~/.cache/huggingface/lerobot")
    cache_path = cache_path.replace('/', os.path.sep)  # 兼容Windows路径格式
    
    # 正确的动作目录路径
    action_dir = os.path.join(cache_path, "angle2001", record_name)
    print(f"尝试删除动作: {record_name}, 路径: {action_dir}")
    
    try:
        if os.path.exists(action_dir) and os.path.isdir(action_dir):
            import shutil
            shutil.rmtree(action_dir)
            print(f"已成功删除动作目录: {action_dir}")
            return {"status": "success", "message": f"已删除动作: {record_name}"}
        else:
            print(f"找不到动作目录: {action_dir}")
            raise HTTPException(status_code=404, detail=f"找不到动作记录: {record_name}")
    except Exception as e:
        print(f"删除动作时出错: {e}")
        raise HTTPException(status_code=500, detail=f"删除动作时出错: {e}")

# 录制预热函数
def warmup_record(robot, events, enable_teleoperation, warmup_time_s, display_data, fps):
    """录制预热阶段"""
    print(f"录制预热开始，时长{warmup_time_s}秒...")
    start_time = time.time()
    while time.time() - start_time < warmup_time_s:
        # 如果启用遥控操作，可以在这里处理遥控指令
        if enable_teleoperation:
            pass  # 实际项目中可以添加遥控处理逻辑
        
        # 休眠以控制循环频率
        time.sleep(1.0 / fps)
    print("录制预热完成")

# 重置环境
def reset_environment(robot, events, reset_time_s, fps):
    """重置环境，准备下一次录制"""
    print(f"重置环境，时长{reset_time_s}秒...")
    start_time = time.time()
    while time.time() - start_time < reset_time_s:
        # 检查是否需要提前退出
        if events.get("stop_recording", False):
            print("提前结束重置")
            break
        
        # 休眠以控制循环频率
        time.sleep(1.0 / fps)
    print("环境重置完成")

# 停止录制
def stop_recording(robot, listener, display_data):
    """停止录制，清理资源"""
    print("停止录制，清理资源...")
    
    # 如果有键盘监听器，停止监听
    if listener:
        # 实际项目中可以添加停止键盘监听的逻辑
        pass
    
    # 关闭显示窗口
    if display_data:
        # 实际项目中可以添加关闭窗口的逻辑
        pass
    
    print("录制资源已清理")

# 初始化键盘监听器
def init_keyboard_listener():
    """初始化键盘监听器"""
    print("初始化键盘监听器...")
    events = {
        "stop_recording": False,
        "exit_early": False,
        "rerecord_episode": False
    }
    
    # 创建一个模拟的监听器
    listener = {
        "start": lambda: None,
        "stop": lambda: None
    }
    
    # 实际项目中可以添加真实的键盘监听逻辑
    
    return listener, events

# 精简版录制功能
def record_simplified(
    robot: Any,
    cfg: RecordControlConfig,
):
    """
    按照用户提供的模板实现的录制功能:
    - 与control_robot.py中的record函数逻辑保持一致
    - 使用LeRobotDataset直接处理数据
    - 避免访问HuggingFace
    """
    # 创建新的数据集
    print(f"准备创建数据集，repo_id: {cfg.repo_id}")
    dataset = None
    try:
        # 检查数据集名称
        sanity_check_dataset_name(cfg.repo_id, policy_cfg=None)
        # 创建数据集（与control_robot.py中逻辑一致，使用LeRobot的默认缓存路径）
        dataset = LeRobotDataset.create(
            cfg.repo_id,
            cfg.fps,
            # 不指定root，让LeRobotDataset使用默认路径
            robot=robot,
            use_videos=False,  # 不使用视频以避免额外依赖
            image_writer_processes=0,
            image_writer_threads=0
        )
    except Exception as e:
        print(f"创建数据集时出错: {e}")
        return None
    
    # 确保机器人已连接（与control_robot.py一致）
    if not robot.is_connected:
        robot.connect()
        print("机器人连接成功")

    # 初始化键盘监听器（与control_robot.py一致）
    listener, events = init_keyboard_listener()
    
    # 跳过warmup阶段以简化流程（不同于control_robot.py）
    print("跳过warmup阶段以简化流程")
    
    # 如果机器人支持teleop_safety_stop，调用它（与control_robot.py一致）
    if has_method(robot, "teleop_safety_stop"):
        try:
            robot.teleop_safety_stop()
        except Exception as e:
            print(f"执行teleop_safety_stop失败: {e}")
    
    # 只录制一个片段（简化逻辑）
    print(f"开始录制片段，目标时长: {cfg.episode_time_s}秒")
    try:
        record_episode(
            robot=robot,
            dataset=dataset,
            events=events,
            episode_time_s=cfg.episode_time_s,
            display_data=cfg.display_data,
            policy=None,  # 不使用策略
            fps=cfg.fps,
            single_task=cfg.single_task,
        )
    except KeyError as e:
        print(f"录制过程中出现KeyError: {e}，尝试使用自定义方法录制")
        # 自定义录制方法，避免使用record_episode的'main'键依赖
        custom_record_data(robot, dataset, cfg.episode_time_s, cfg.fps, cfg.single_task)
    

    dataset.save_episode()
    print(f"成功保存片段，总帧数: {dataset.num_frames if hasattr(dataset, 'num_frames') else '未知'}")
    
    # 返回数据集（与control_robot.py一致）
    return dataset

# 重播逻辑
def replay(robot, cfg):
    """重播录制的动作数据"""
    print("开始时间", time.time())
    
    try:
        # 尝试加载数据集 (适用于使用LeRobotDataset格式的情况)
        dataset = LeRobotDataset(cfg.repo_id, root=cfg.root, episodes=[cfg.episode])
        actions = dataset.hf_dataset.select_columns("action")
        
        if not robot.is_connected:
            robot.connect()
        print("连接时间", time.time())
        
        for idx in range(dataset.num_frames):
            # 跳过偶数帧，减少数据量
            if idx % 2 == 0:
                continue
                
            start_t = time.perf_counter()
            
            # 获取当前帧的动作数据
            action = actions[idx]["action"]
            # 发送动作到机器人
            robot.send_action(action)
            
            # 控制回放帧率
            elapsed = time.perf_counter() - start_t
            time.sleep(max(0, 1 / cfg.fps - elapsed))
            
            # 记录控制信息 (安全帧间隔值)
            safe_dt = max(elapsed, 1e-6)
            log_control_info(robot, safe_dt, fps=cfg.fps)
        
        print("结束时间", time.time())
        return True, "重播完成"
    except Exception as e:
        error_msg = f"重播出错: {e}"
        print(f"详细错误: {repr(e)}")
        return False, error_msg

# 重播本地录制的动作数据
def replay_local_recording(robot, record_name, fps=30, skip_frames=True):
    """重播本地录制的动作数据"""
    print(f"开始重播动作 '{record_name}'，时间: {time.time()}")
    
    # 检查动作是否存在内存中
    if record_name not in action_recordings:
        # 尝试从磁盘加载
        found = False
        for filename in os.listdir(RECORDINGS_DIR):
            if filename.startswith(f"{record_name}_") and filename.endswith(".json"):
                file_path = os.path.join(RECORDINGS_DIR, filename)
                try:
                    with open(file_path, 'r') as f:
                        actions_data = json.load(f)
                    
                    timestamp = filename.split('_')[-1].split('.')[0]
                    action_recordings[record_name] = {
                        "path": file_path,
                        "data": actions_data,
                        "timestamp": timestamp
                    }
                    found = True
                    break
                except Exception as e:
                    print(f"加载动作文件 {file_path} 出错: {e}")
        
        if not found:
            return False, f"找不到动作记录: {record_name}"
    
    # 获取动作数据
    actions_data = action_recordings[record_name]["data"]
    
    try:
        print(f"开始重播动作 '{record_name}'，共 {len(actions_data)} 帧")
        
        start_time = time.time()
        played_frames = 0
        
        # 获取臂的控制器
        main_arm_control = None
        if robot and hasattr(robot, 'leader_arms') and isinstance(robot.leader_arms, dict):
            main_arm_control = robot.leader_arms.get("main")
        
        follower_arm_control = None
        if robot and hasattr(robot, 'follower_arms') and isinstance(robot.follower_arms, dict):
            follower_arm_control = robot.follower_arms.get("main")
        
        for i, frame in enumerate(actions_data):
            # 跳过偶数帧，减少数据量
            if skip_frames and i % 2 == 0:
                continue
            
            start_t = time.perf_counter()
            
            # 发送主臂状态
            main_state = frame.get("main_arm")
            if main_arm_control is not None and main_state and has_method(main_arm_control, 'send_state'):
                main_arm_control.send_state(main_state)
            
            # 发送从臂状态
            follower_state = frame.get("follower_arm")
            if follower_arm_control is not None and follower_state and has_method(follower_arm_control, 'send_state'):
                follower_arm_control.send_state(follower_state)
            
            # 控制回放帧率
            elapsed = time.perf_counter() - start_t
            time.sleep(max(0, 1 / fps - elapsed))
            
            # 记录控制信息
            safe_dt = max(elapsed, 1e-6)
            if hasattr(robot, 'log_info'):
                robot.log_info(safe_dt, fps=fps)
            
            played_frames += 1
            
            # 显示进度
            if played_frames % 30 == 0:
                print(f"重播进度: {played_frames}/{len(actions_data)//2 if skip_frames else len(actions_data)}")
        
        end_time = time.time()
        print(f"结束重播，耗时: {end_time - start_time:.2f}秒")
        return True, f"重播完成，共播放 {played_frames} 帧"
    
    except Exception as e:
        error_msg = f"重播时出错: {e}"
        print(f"详细错误: {repr(e)}")
        return False, error_msg 

# 自定义录制方法，不依赖'main'键
def custom_record_data(robot, dataset, episode_time_s, fps, task_description):
    """
    自定义录制方法，避免使用可能导致KeyError的record_episode函数
    """
    print(f"使用自定义录制方法，时长: {episode_time_s}秒")
    
    # 开始录制
    start_time = time.time()
    frame_count = 0
    
    # 获取初始机器人状态，避免直接访问'main'
    initial_state = {}
    try:
        if hasattr(robot, "get_state"):
            initial_state = robot.get_state()
        else:
            # 尝试分别获取主臂和从臂的状态
            if hasattr(robot, "leader_arms"):
                for arm_name, arm in robot.leader_arms.items():
                    if hasattr(arm, "get_state"):
                        initial_state[f"leader_{arm_name}"] = arm.get_state()
            
            if hasattr(robot, "follower_arms"):
                for arm_name, arm in robot.follower_arms.items():
                    if hasattr(arm, "get_state"):
                        initial_state[f"follower_{arm_name}"] = arm.get_state()
    except Exception as e:
        print(f"获取初始状态出错: {e}")
    
    # 确保数据集中有最小必需字段
    task = task_description if isinstance(task_description, str) else "Perform the robot action"
    
    while time.time() - start_time < episode_time_s:
        try:
            # 获取当前时间点
            t = time.time() - start_time
            
            # 帧时间戳
            timestamp = {"t": t}
            
            # 获取机器人状态，避免直接访问'main'
            observation = {"state": {}}
            action = {}
            
            # 尝试获取机器人整体状态
            if hasattr(robot, "get_state"):
                observation["state"] = robot.get_state()
            else:
                # 分别获取各个臂的状态
                if hasattr(robot, "leader_arms"):
                    for arm_name, arm in robot.leader_arms.items():
                        if hasattr(arm, "get_state"):
                            arm_state = arm.get_state()
                            observation["state"][f"leader_{arm_name}"] = arm_state
                            action[f"leader_{arm_name}"] = arm_state  # 使用状态作为动作
                
                if hasattr(robot, "follower_arms"):
                    for arm_name, arm in robot.follower_arms.items():
                        if hasattr(arm, "get_state"):
                            arm_state = arm.get_state()
                            observation["state"][f"follower_{arm_name}"] = arm_state
                            action[f"follower_{arm_name}"] = arm_state  # 使用状态作为动作
            
            # 确保action字段存在
            if not action and "state" in observation:
                action = observation["state"]  # 如果没有单独的动作，使用状态作为动作
            
            # 添加任务描述
            task_data = {"task": task}
            
            # 数据组合在一起
            combined_data = {
                **timestamp,
                "observation": observation,
                "action": action,
                "task": task
            }
            
            # 添加到数据集
            if hasattr(dataset, "add_step"):
                dataset.add_step(**combined_data)
            else:
                print("警告: 数据集没有add_step方法，尝试使用替代方法")
                # 尝试直接添加到内部数据结构
                if hasattr(dataset, "_data"):
                    dataset._data.append(combined_data)
                elif hasattr(dataset, "data"):
                    dataset.data.append(combined_data)
                else:
                    print("错误: 无法添加数据到数据集")
            
            frame_count += 1
            
            # 休眠以维持帧率
            elapsed = time.time() - (start_time + t)
            sleep_time = max(0, 1/fps - elapsed)
            time.sleep(sleep_time)
            
            # 每秒显示一次进度
            if frame_count % fps == 0:
                elapsed_time = time.time() - start_time
                remaining_time = max(0, episode_time_s - elapsed_time)
                print(f"已录制 {frame_count} 帧，已用时间: {elapsed_time:.1f}秒，剩余时间: {remaining_time:.1f}秒")
                
        except Exception as e:
            print(f"录制帧时出错: {e}")
            # 继续尝试录制下一帧
    
    # 录制结束
    total_time = time.time() - start_time
    print(f"自定义录制完成，共录制 {frame_count} 帧，用时 {total_time:.1f} 秒")
    
    # 设置数据集属性
    if hasattr(dataset, "num_frames"):
        dataset.num_frames = frame_count
    
    return frame_count

if __name__ == "__main__":
    import uvicorn
    from fastapi import FastAPI

    # 创建一个 FastAPI 应用实例
    app = FastAPI(title="Orange Pi Robot Arm Service")

    # 将 action_replay 路由包含进来，并设置统一前缀
    # 这样前端之前使用的 /api/action_replay/list 等路径依然有效
    app.include_router(router, prefix="/api/action_replay")

    print("将在 http://0.0.0.0:8002/api/action_replay/... 启动服务")
    # 运行 FastAPI 应用，监听在 0.0.0.0 (所有网络接口) 的 8002 端口
    # 在香橙派上，您可以通过其 IP 地址 (例如 ************:8002) 从局域网内其他设备访问
    uvicorn.run(app, host="0.0.0.0", port=8002) 