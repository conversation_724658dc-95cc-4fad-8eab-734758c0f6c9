import os
from openai import OpenAI

client = OpenAI(
    # 从环境变量中读取您的方舟API Key
    api_key=os.environ.get("ARK_API_KEY"), 
    base_url="https://ark.cn-beijing.volces.com/api/v3",
    # 深度思考模型耗费时间会较长，建议您设置一个较长的超时时间，推荐为30分钟
    timeout=1800,
    )
response = client.chat.completions.create(
    # 替换 <MODEL> 为 Model ID
    model="doubao-seed-1-6-250615",
    messages=[
        {"role": "user", "content": "我要研究深度思考模型与非深度思考模型区别的课题，怎么体现我的专业性"}
    ]
)
# 当触发深度思考时，打印思维链内容
if hasattr(response.choices[0].message, 'reasoning_content'):
    print(response.choices[0].message.reasoning_content)
print(response.choices[0].message.content)
#多轮对话
import os
from openai import OpenAI

client = OpenAI(
    # 从环境变量中读取您的方舟API Key
    api_key=os.environ.get("ARK_API_KEY"), 
    base_url="https://ark.cn-beijing.volces.com/api/v3",
    # 深度思考模型耗费时间会较长，建议您设置一个较长的超时时间，推荐为30分钟
    timeout=1800,
    )
response = client.chat.completions.create(
    # 替换 <MODEL> 为模型的Model ID
    model="<MODEL>",
    messages=[
        {"role": "user", "content": "研究深度思考模型与非深度思考模型区别"},
        {"role": "assistant", "content": "推理模型主要依靠逻辑、规则或概率等进行分析、推导和判断以得出结论或决策，非推理模型则是通过模式识别、统计分析或模拟等方式来实现数据描述、分类、聚类或生成等任务而不依赖显式逻辑推理。"},
        {"role": "user", "content": "我要研究深度思考模型与非深度思考模型区别的课题，怎么体现我的专业性"},
    ],
)

if hasattr(response.choices[0].message, 'reasoning_content'):
    print(response.choices[0].message.reasoning_content)
print(response.choices[0].message.content)
#流式输出
from openai import OpenAI
import os
client = OpenAI(
    # 从环境变量中读取您的方舟API Key
    api_key=os.environ.get("ARK_API_KEY"), 
    base_url="https://ark.cn-beijing.volces.com/api/v3",
    )
messages = [{"role": "user", "content": "深度思考模型与非深度思考模型的区别是什么"}]
response = client.chat.completions.create(
    # 替换 <MODEL> 为模型的Model ID
    model="<MODEL>",
    messages=messages,
    stream=True,
)
reasoning_content = ""
content = ""
with response:
    for chunk in response:
        if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
            reasoning_content += chunk.choices[0].delta.reasoning_content
            print(chunk.choices[0].delta.reasoning_content, end="")
        else:
            content += chunk.choices[0].delta.content
            print(chunk.choices[0].delta.content, end="")
#图像理解     
import os
from openai import OpenAI

# 请确保您已将 API Key 存储在环境变量 ARK_API_KEY 中
# 初始化Ark客户端，从环境变量中读取您的API Key
client = OpenAI(
    # 此为默认路径，您可根据业务所在地域进行配置
    base_url="https://ark.cn-beijing.volces.com/api/v3",
    # 从环境变量中获取您的 API Key。此为默认方式，您可根据需要进行修改
    api_key=os.environ.get("ARK_API_KEY"),
)

response = client.chat.completions.create(
    # 指定您创建的方舟推理接入点 ID，此处已帮您修改为您的推理接入点 ID
    model="doubao-1-5-thinking-pro-m-250415",
    messages=[
        {
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": "https://ark-project.tos-cn-beijing.volces.com/images/view.jpeg"
                    },
                },
                {"type": "text", "text": "这是哪里？"},
            ],
        }
    ],
)

print(response.choices[0])
#base64
import base64
import os
# 通过 pip install volcengine-python-sdk[ark] 安装方舟SDK
from volcenginesdkarkruntime import Ark

# 初始化一个Client对象，从环境变量中获取API Key
client = Ark(
    api_key=os.getenv('ARK_API_KEY'),
    )

# 定义方法将指定路径图片转为Base64编码
def encode_image(image_path):
  with open(image_path, "rb") as image_file:
    return base64.b64encode(image_file.read()).decode('utf-8')

# 需要传给大模型的图片
image_path = "path_to_your_image.jpg"

# 将图片转为Base64编码
base64_image = encode_image(image_path)

response = client.chat.completions.create(
  # 替换 <MODEL> 为模型的Model ID
  model="<MODEL>",
  messages=[
    {
      "role": "user",
      "content": [
        {
          "type": "image_url",
          "image_url": {
          # 需要注意：传入Base64编码前需要增加前缀 data:image/{图片格式};base64,{Base64编码}：
          # PNG图片："url":  f"data:image/png;base64,{base64_image}"
          # JPEG图片："url":  f"data:image/jpeg;base64,{base64_image}"
          # WEBP图片："url":  f"data:image/webp;base64,{base64_image}"
            "url":  f"data:image/<IMAGE_FORMAT>;base64,{base64_image}"
          },         
        },
        {
          "type": "text",
          "text": "图里有什么",
        },
      ],
    }
  ],
)

print(response.choices[0])
#控制图片理解的精细度
控制图片理解的精细度
控制图片理解的精细度（指对画面的精细）： image_pixel_limit 、detail 字段，2个字段若同时配置，则生效逻辑如下：

生效优先级：image_pixel_limit 高于 detail 字段，即同时配置 detail 与 image_pixel_limit 字段时，生效 image_pixel_limit 字段配置**。**
缺省时逻辑：image_pixel_limit 字段的 min_pixels / max_pixels 字段未设置，则使用 detail （默认值为low）设置配置的值对应的min_pixels 值 3136 **** / max_pixels 值1048576。
下面分别介绍如何通过 detail 、 image_pixel_limit 控制视觉理解的精度。

通过 detail 字段（图片理解）
你可以通过detail参数来控制模型理解图片的精细度，以及返回速度，计费公式请参见token 用量说明。

low：“低分辨率”模式，处理速度会提高，适合图片本身细节较少或者只需要模型理解图片大致信息或者对速度有要求的场景。此时 min_pixels 取值3136、max_pixels 取值1048576，超出此像素范围且小于3600w px的图片（超出3600w px 会直接报错）将会等比例缩放至范围内。
high：“高分辨率”模式，这代表模型会理解图片更多的细节，但是处理图片速度会降低，适合需要模型理解图像细节，图像细节丰富，需要关注图片细节的场景。此时 min_pixels 取值3136、max_pixels 取值4014080，超出此像素范围且小于3600w px的图片（超出3600w px 会直接报错）的图片将会等比例缩放至范围内。
auto：采用“低分辨率”模式。
新版模型（doubao-1-5-vision-pro-32k-250115及以后版本）：采用low模式。
旧版模型（doubao-vision-pro-32k-241028、doubao-vision-lite-32k-241025）：根据图片分辨率，自行选择模式。
import os
# 可通过 pip install volcengine-python-sdk[ark] 安装方舟SDK 
from volcenginesdkarkruntime import Ark

# 初始化一个Client对象，从环境变量中获取API Key
client = Ark(
    api_key=os.getenv('ARK_API_KEY'),
    )

# 调用 Ark 客户端的 chat.completions.create 方法创建聊天补全请求
response = client.chat.completions.create(
    # 替换 <MODEL> 为模型的Model ID
    model="<MODEL>",
    messages=[
        {
            # 消息角色为用户
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    # 第一张图片链接及细节设置为 high
                    "image_url": {
                        # 您可以替换图片链接为您的实际图片链接
                        "url":  "https://ark-project.tos-cn-beijing.volces.com/doc_image/ark_demo_img_1.png",
                        "detail": "high"
                    }
                },
                # 文本类型的消息内容，询问图片里有什么
                {"type": "text", "text": "图片里有什么？"},
            ],
        }
    ],
)

print(response.choices[0])