from fastapi import APIRouter, HTTPException, UploadFile, File, Form, Query
from fastapi.responses import FileResponse
from typing import List, Optional
import os
import shutil
import time
from pathlib import Path
import httpx
import asyncio

# 创建路由
router = APIRouter()

# 数据集根目录 - 使用绝对路径
DATASET_DIR = os.path.abspath(os.environ.get("DATASET_DIR", "./datasets"))

# 确保数据集目录存在
os.makedirs(DATASET_DIR, exist_ok=True)

# 香橙派默认配置
DEFAULT_ORANGE_PI_HOST = "************"
DEFAULT_ORANGE_PI_SSH_PORT = 22
DEFAULT_ORANGE_PI_USERNAME = "root"
DEFAULT_ORANGE_PI_PASSWORD = "Mind@123"  # 在实际应用中应该使用更安全的方式存储密码

# 获取文件列表
@router.get("/list")
async def list_files(path: Optional[str] = Query(None)):
    """
    获取指定路径下的文件列表
    """
    try:
        # 确定要列出的目录
        target_dir = DATASET_DIR if path is None else os.path.join(DATASET_DIR, path)
        target_dir = os.path.abspath(target_dir)
        
        print(f"请求目录: {target_dir}")
        
        # 确保路径在数据集目录内（防止目录遍历攻击）
        # 使用目录字符串比较，确保在Windows上正常工作
        if not os.path.commonpath([target_dir, DATASET_DIR]) == DATASET_DIR:
            raise HTTPException(status_code=403, detail=f"禁止访问数据集目录之外的文件: {target_dir} 不在 {DATASET_DIR} 内")
        
        # 检查目录是否存在
        if not os.path.exists(target_dir):
            # 如果目录不存在，尝试创建它
            try:
                os.makedirs(target_dir, exist_ok=True)
                print(f"已创建目录: {target_dir}")
            except Exception as e:
                print(f"创建目录失败: {e}")
                raise HTTPException(status_code=404, detail=f"路径不存在且无法创建: {path}")
        
        # 获取文件列表
        files_and_dirs = []
        for item in os.listdir(target_dir):
            item_path = os.path.join(target_dir, item)
            try:
                size = os.path.getsize(item_path)
            except:
                size = 0
                
            try:
                modified = os.path.getmtime(item_path)
            except:
                modified = 0
                
            item_info = {
                "name": item,
                "is_dir": os.path.isdir(item_path),
                "size": size,
                "modified": modified
            }
            files_and_dirs.append(item_info)
            
        # 按类型和名称排序
        files_and_dirs.sort(key=lambda x: (not x["is_dir"], x["name"]))
        
        return {
            "path": path or "",
            "items": files_and_dirs
        }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        print(f"获取文件列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文件列表失败: {str(e)}")

# 香橙派目录列表
@router.get("/orange_pi/list")
async def list_orange_pi_directories(
    directory: str = Query("/root/.cache/huggingface/lerobot/angle2001", description="香橙派上要列出的目录路径"),
    host: str = Query(DEFAULT_ORANGE_PI_HOST, description="香橙派主机地址"),
    username: str = Query(DEFAULT_ORANGE_PI_USERNAME, description="SSH用户名"),
    password: str = Query(DEFAULT_ORANGE_PI_PASSWORD, description="SSH密码"),
    port: int = Query(DEFAULT_ORANGE_PI_SSH_PORT, description="SSH端口")
):
    """
    获取香橙派指定目录下的文件和目录列表
    """
    try:
        import paramiko
        
        # 创建SSH客户端
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        try:
            # 连接香橙派
            ssh.connect(hostname=host, port=port, username=username, password=password, timeout=10)
            
            # 执行命令列出目录
            cmd = f"ls -la {directory}"
            stdin, stdout, stderr = ssh.exec_command(cmd)
            
            # 读取命令输出
            output = stdout.read().decode()
            error = stderr.read().decode()
            
            if error and not output:
                raise Exception(f"列出目录失败: {error}")
            
            # 解析输出获取文件和目录
            lines = output.strip().split('\n')
            items = []
            
            for line in lines:
                parts = line.split()
                if len(parts) >= 9:  # ls -la 输出格式
                    permissions = parts[0]
                    name = parts[-1]
                    
                    # 跳过 . 和 .. 目录
                    if name in ['.', '..']:
                        continue
                    
                    # 检查是否为目录
                    is_dir = permissions[0] == 'd'
                    
                    # 计算文件大小（以字节为单位）
                    try:
                        size = int(parts[4])
                    except:
                        size = 0
                    
                    items.append({
                        "name": name,
                        "is_dir": is_dir,
                        "path": f"{directory}/{name}",
                        "size": size,
                        # 使用ls输出的修改时间
                        "modified": f"{parts[5]} {parts[6]} {parts[7]}"
                    })
            
            return {
                "path": directory,
                "items": items
            }
            
        finally:
            # 关闭SSH连接
            ssh.close()
            
    except ImportError:
        raise HTTPException(
            status_code=500, 
            detail="缺少必要的依赖，请安装paramiko库: pip install paramiko"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"连接香橙派或获取目录列表失败: {str(e)}"
        )

# 香橙派HTTP服务获取目录列表（备选方案）
@router.get("/orange_pi/http_list")
async def list_orange_pi_directories_http(
    directory: str = Query("/root/.cache/huggingface/lerobot/angle2001", description="香橙派上要列出的目录路径"),
    host: str = Query(DEFAULT_ORANGE_PI_HOST, description="香橙派主机地址"),
    port: int = Query(8080, description="香橙派HTTP服务端口")
):
    """
    通过HTTP服务获取香橙派指定目录下的子目录列表（如果香橙派运行了HTTP文件服务）
    """
    try:
        url = f"http://{host}:{port}/list"
        params = {"path": directory}
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=params, timeout=10.0)
            response.raise_for_status()
            return response.json()
            
    except httpx.HTTPStatusError as e:
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"香橙派HTTP服务返回错误: {e.response.text}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"连接香橙派HTTP服务或获取目录列表失败: {str(e)}"
        )

# 获取文件内容
@router.get("/file/{file_path:path}")
async def get_file(file_path: str):
    """
    获取指定文件的内容（下载文件）
    """
    try:
        # 构建文件路径
        target_file = os.path.join(DATASET_DIR, file_path)
        target_file = os.path.abspath(target_file)
        
        # 确保路径在数据集目录内
        if not os.path.commonpath([target_file, DATASET_DIR]) == DATASET_DIR:
            raise HTTPException(status_code=403, detail=f"禁止访问数据集目录之外的文件: {target_file} 不在 {DATASET_DIR} 内")
        
        # 检查文件是否存在
        if not os.path.exists(target_file):
            raise HTTPException(status_code=404, detail=f"文件不存在: {file_path}")
        
        if os.path.isdir(target_file):
            raise HTTPException(status_code=400, detail=f"目标是一个目录，不能下载: {file_path}")
        
        # 返回文件内容
        return FileResponse(target_file, filename=os.path.basename(target_file))
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"获取文件内容失败: {str(e)}")

# 上传文件
@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    path: Optional[str] = Form(None)
):
    """
    上传文件到指定路径
    """
    try:
        # 确定文件保存路径
        target_dir = DATASET_DIR if path is None else os.path.join(DATASET_DIR, path)
        target_dir = os.path.abspath(target_dir)
        
        # 确保路径在数据集目录内
        if not os.path.commonpath([target_dir, DATASET_DIR]) == DATASET_DIR:
            raise HTTPException(status_code=403, detail=f"禁止访问数据集目录之外的文件: {target_dir} 不在 {DATASET_DIR} 内")
        
        # 检查目标目录是否存在
        if not os.path.exists(target_dir):
            os.makedirs(target_dir)
        
        # 保存文件
        file_path = os.path.join(target_dir, file.filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        return {
            "status": "success",
            "message": f"文件上传成功: {file.filename}",
            "path": os.path.join(path or "", file.filename)
        }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

# 删除文件
@router.delete("/file/{file_path:path}")
async def delete_file(file_path: str):
    """
    删除指定路径的文件或目录
    """
    # 不再执行实际删除，直接返回权限错误
    raise HTTPException(status_code=403, detail="你没有权限删除，请联系管理员")

# 创建目录
@router.post("/mkdir")
async def create_directory(path: str = Form(...)):
    """
    在指定路径创建新目录
    """
    try:
        # 构建目录路径
        target_dir = os.path.join(DATASET_DIR, path)
        target_dir = os.path.abspath(target_dir)
        
        # 确保路径在数据集目录内
        if not os.path.commonpath([target_dir, DATASET_DIR]) == DATASET_DIR:
            raise HTTPException(status_code=403, detail=f"禁止创建数据集目录之外的目录: {target_dir} 不在 {DATASET_DIR} 内")
        
        # 检查目录是否已存在
        if os.path.exists(target_dir):
            raise HTTPException(status_code=400, detail=f"目录已存在: {path}")
        
        # 创建目录
        os.makedirs(target_dir)
        
        return {
            "status": "success",
            "message": f"目录创建成功: {path}"
        }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"创建目录失败: {str(e)}")

# 香橙派删除文件或目录
@router.delete("/orange_pi/delete")
async def delete_orange_pi_file(
    file_path: str = Query(..., description="要删除的文件或目录的完整路径"),
    host: str = Query(DEFAULT_ORANGE_PI_HOST, description="香橙派主机地址"),
    username: str = Query(DEFAULT_ORANGE_PI_USERNAME, description="SSH用户名"),
    password: str = Query(DEFAULT_ORANGE_PI_PASSWORD, description="SSH密码"),
    port: int = Query(DEFAULT_ORANGE_PI_SSH_PORT, description="SSH端口"),
    is_dir: bool = Query(False, description="是否为目录")
):
    """
    删除香橙派上的文件或目录
    """
    # 不再执行实际删除，直接返回权限错误
    raise HTTPException(status_code=403, detail="你没有权限删除，请联系管理员") 