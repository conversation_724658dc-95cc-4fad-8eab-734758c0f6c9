#!/usr/bin/env python
# 创建测试图像脚本

from PIL import Image, ImageDraw, ImageFont
import numpy as np

def create_robot_arm_test_image():
    """创建一个模拟机械臂场景的测试图像"""
    
    # 创建画布
    width, height = 800, 600
    image = Image.new('RGB', (width, height), color='lightgray')
    draw = ImageDraw.Draw(image)
    
    # 绘制桌面
    table_color = 'brown'
    draw.rectangle([0, height-100, width, height], fill=table_color)
    
    # 绘制一些物体
    # 红色方块
    red_box = [150, height-150, 200, height-100]
    draw.rectangle(red_box, fill='red', outline='darkred', width=2)
    
    # 蓝色圆形
    blue_circle = [300, height-140, 350, height-90]
    draw.ellipse(blue_circle, fill='blue', outline='darkblue', width=2)
    
    # 绿色三角形（用多边形近似）
    green_triangle = [(500, height-100), (475, height-150), (525, height-150)]
    draw.polygon(green_triangle, fill='green', outline='darkgreen', width=2)
    
    # 绘制机械臂（简化版）
    # 机械臂基座
    base_center = (650, height-100)
    draw.ellipse([base_center[0]-30, base_center[1]-20, base_center[0]+30, base_center[1]+20], 
                 fill='gray', outline='black', width=2)
    
    # 机械臂臂段
    arm_segments = [
        (base_center, (620, height-200)),  # 第一段
        ((620, height-200), (580, height-250)),  # 第二段
        ((580, height-250), (550, height-280))   # 末端执行器
    ]
    
    for start, end in arm_segments:
        draw.line([start, end], fill='black', width=8)
        # 关节
        draw.ellipse([start[0]-5, start[1]-5, start[0]+5, start[1]+5], 
                     fill='darkgray', outline='black', width=1)
    
    # 末端执行器（夹爪）
    gripper_center = arm_segments[-1][1]
    draw.line([gripper_center[0]-10, gripper_center[1]-5, gripper_center[0]-10, gripper_center[1]+5], 
              fill='black', width=3)
    draw.line([gripper_center[0]+10, gripper_center[1]-5, gripper_center[0]+10, gripper_center[1]+5], 
              fill='black', width=3)
    
    # 添加文字标签
    try:
        # 尝试使用默认字体
        font = ImageFont.load_default()
    except:
        font = None
    
    # 标注物体
    draw.text((155, height-180), "红色方块", fill='black', font=font)
    draw.text((305, height-170), "蓝色圆形", fill='black', font=font)
    draw.text((480, height-180), "绿色三角", fill='black', font=font)
    draw.text((600, height-320), "机械臂", fill='black', font=font)
    
    # 添加标题
    draw.text((20, 20), "机械臂控制测试场景", fill='black', font=font)
    draw.text((20, 50), "任务：请抓取红色方块", fill='darkred', font=font)
    
    return image

def create_simple_test_image():
    """创建一个简单的测试图像"""
    
    # 创建画布
    width, height = 400, 300
    image = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(image)
    
    # 绘制一个简单的场景
    # 背景
    draw.rectangle([0, 0, width, height], fill='lightblue')
    
    # 地面
    draw.rectangle([0, height-50, width, height], fill='green')
    
    # 一个红色的目标物体
    target = [width//2-25, height-100, width//2+25, height-50]
    draw.rectangle(target, fill='red', outline='darkred', width=2)
    
    # 添加文字
    try:
        font = ImageFont.load_default()
    except:
        font = None
    
    draw.text((10, 10), "简单测试场景", fill='black', font=font)
    draw.text((10, 30), "目标：红色方块", fill='darkred', font=font)
    
    return image

def main():
    """主函数"""
    print("创建测试图像...")
    
    # 创建机械臂测试图像
    robot_image = create_robot_arm_test_image()
    robot_image.save("robot_arm_test_scene.jpg", quality=90)
    print("✅ 机械臂测试场景图像已保存: robot_arm_test_scene.jpg")
    
    # 创建简单测试图像
    simple_image = create_simple_test_image()
    simple_image.save("simple_test_scene.jpg", quality=90)
    print("✅ 简单测试场景图像已保存: simple_test_scene.jpg")
    
    print("\n📋 使用说明:")
    print("1. 启动Web服务器: python main.py")
    print("2. 访问: http://localhost:8088/web/")
    print("3. 上传测试图像")
    print("4. 输入任务描述，例如：")
    print("   - '请抓取红色方块'")
    print("   - '分析当前场景并制定抓取计划'")
    print("   - '移动到红色物体附近'")

if __name__ == "__main__":
    main()
